import os
import docx
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

def insert_visuals_in_sections():
    # Pad naar het rapport
    report_path = "Adviesrapport Veranderingsmanagement-<PERSON><PERSON>-1066741.docx"
    visuals_folder = "Exported_Slides"
    
    if not os.path.exists(report_path):
        print(f"Rapport niet gevonden: {report_path}")
        return
    
    # Open het document
    doc = docx.Document(report_path)
    
    print("Visuals worden geplaatst in de juiste secties...")
    
    # Zoek naar secties en voeg visuals toe
    for i, para in enumerate(doc.paragraphs):
        text = para.text.lower()
        
        # Kotter sectie
        if "kotter" in text and "8-stappenmodel" in text:
            print("Visual toegevoegd bij Kotter sectie")
            # Voeg Kotter visual toe
            kotter_visual = os.path.join(visuals_folder, "2. Kotter/slide/Slide1.PNG")
            if os.path.exists(kotter_visual):
                doc.add_picture(kotter_visual, width=Inches(6))
                doc.add_paragraph("Figuur 1: Kotter 8-stappenmodel")
        
        # Caluwé sectie
        elif "caluwé" in text and "kleurenmodel" in text:
            print("Visual toegevoegd bij Caluwé sectie")
            # Voeg Caluwé visual toe
            caluwe_visual = os.path.join(visuals_folder, "5. Caluwe/slide/Slide1.PNG")
            if os.path.exists(caluwe_visual):
                doc.add_picture(caluwe_visual, width=Inches(6))
                doc.add_paragraph("Figuur 2: Caluwé Kleurenmodel")
        
        # Six Sigma sectie
        elif "six sigma" in text and "dmaic" in text:
            print("Visual toegevoegd bij Six Sigma sectie")
            # Voeg DMAIC visual toe
            dmaic_visual = os.path.join(visuals_folder, "2. Veranderaanpak/slide/Slide1.PNG")
            if os.path.exists(dmaic_visual):
                doc.add_picture(dmaic_visual, width=Inches(6))
                doc.add_paragraph("Figuur 3: Six Sigma DMAIC Framework")
        
        # Stakeholderanalyse sectie
        elif "stakeholderanalyse" in text:
            print("Visual toegevoegd bij Stakeholderanalyse sectie")
            # Voeg stakeholder visual toe
            stakeholder_visual = os.path.join(visuals_folder, "4. Stakeholderanalyse/slide/Slide4.PNG")
            if os.path.exists(stakeholder_visual):
                doc.add_picture(stakeholder_visual, width=Inches(6))
                doc.add_paragraph("Figuur 4: Stakeholder Matrix")
        
        # Communicatieplan sectie
        elif "communicatieplan" in text and "strategie" in text:
            print("Visual toegevoegd bij Communicatieplan sectie")
            # Voeg communicatie visual toe
            comm_visual = os.path.join(visuals_folder, "6. Communicatieplan/slide/Slide1.PNG")
            if os.path.exists(comm_visual):
                doc.add_picture(comm_visual, width=Inches(6))
                doc.add_paragraph("Figuur 5: Communicatieplan Overzicht")
        
        # Interventies sectie
        elif "interventies" in text:
            print("Visual toegevoegd bij Interventies sectie")
            # Voeg interventies visual toe
            interv_visual = os.path.join(visuals_folder, "6. Interventies/slide/Slide1.PNG")
            if os.path.exists(interv_visual):
                doc.add_picture(interv_visual, width=Inches(6))
                doc.add_paragraph("Figuur 6: Typen Interventies")
        
        # Verandermanagement sectie
        elif "verandermanagement" in text:
            print("Visual toegevoegd bij Verandermanagement sectie")
            # Voeg verandermanagement visual toe
            vm_visual = os.path.join(visuals_folder, "0. Verandermanagement/slide/Slide1.PNG")
            if os.path.exists(vm_visual):
                doc.add_picture(vm_visual, width=Inches(6))
                doc.add_paragraph("Figuur 7: Verandermanagement Overzicht")
    
    # Voeg ook een implementatietijdlijn visual toe
    print("Implementatietijdlijn visual toegevoegd")
    timeline_visual = os.path.join(visuals_folder, "2. Veranderaanpak/slide/Slide2.PNG")
    if os.path.exists(timeline_visual):
        # Zoek naar de implementatieplan sectie
        for i, para in enumerate(doc.paragraphs):
            if "implementatieplan" in para.text.lower():
                # Voeg visual toe na deze sectie
                doc.add_picture(timeline_visual, width=Inches(6))
                doc.add_paragraph("Figuur 8: Implementatietijdlijn")
                break
    
    # Sla het document op
    doc.save(report_path)
    print(f"\nVisuals geplaatst in het rapport: {report_path}")

if __name__ == "__main__":
    insert_visuals_in_sections() 