from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os
import re

def update_current_document():
    """Update the current document with content from document (3) and add visuals"""
    
    # Read the current document
    current_doc = Document("Adviesrapport Veranderingsmanagement-<PERSON><PERSON>-1066741.docx")
    
    # Read the content from document (3)
    with open("Adviesrapport Veranderingsmanagement-<PERSON><PERSON>-1066741(3)_Extracted.txt", "r", encoding="utf-8") as f:
        content_3 = f.read()
    
    # Create a new document with updated content
    new_doc = Document()
    
    # Copy the title and basic info from current document
    for paragraph in current_doc.paragraphs[:10]:  # Copy first 10 paragraphs (title, version info)
        new_doc.add_paragraph(paragraph.text)
    
    # Add page break
    new_doc.add_page_break()
    
    # Process the content from document (3) and add visuals
    lines = content_3.split('\n')
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        # Skip empty lines and some headers
        if not line or line.startswith('---'):
            i += 1
            continue
        
        # Check if this line contains a visual placeholder
        if '[Visual' in line and ']' in line:
            # Extract the visual type from the placeholder
            visual_match = re.search(r'\[Visual van (.+?) hier', line)
            if visual_match:
                visual_type = visual_match.group(1)
                
                # Add the text before the visual (clean up the placeholder)
                clean_text = line.replace('[Visual van ' + visual_type + ' hier. Bijvoorbeeld een matrix of schema dat de verschillende strategieën met hun kenmerken en toepassingsgebieden overzichtelijk weergeeft.]', '')
                clean_text = clean_text.replace('[Visual van ' + visual_type + ' hier]', '')
                clean_text = clean_text.replace('[Visual van ' + visual_type + ' hier.]', '')
                
                if clean_text.startswith('**'):
                    # This is a heading, add it
                    heading_text = clean_text.replace('**', '').strip()
                    if heading_text:
                        new_doc.add_heading(heading_text, level=2)
                else:
                    # This is regular text, add it
                    if clean_text.strip():
                        new_doc.add_paragraph(clean_text)
                
                # Add the appropriate visual
                add_visual(new_doc, visual_type)
                
                # Add caption
                caption_text = get_caption_for_visual(visual_type)
                caption = new_doc.add_paragraph(caption_text)
                caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
                
        else:
            # Regular text, add it
            if line.startswith('**'):
                # This is a heading
                heading_text = line.replace('**', '').strip()
                if heading_text:
                    new_doc.add_heading(heading_text, level=1)
            else:
                # Regular paragraph
                if line:
                    new_doc.add_paragraph(line)
        
        i += 1
    
    # Save the updated document
    new_doc.save("Adviesrapport Veranderingsmanagement-Shuja Schadon-1066741_UPDATED.docx")
    print("Document updated successfully with content from document (3) and visuals!")

def add_visual(doc, visual_type):
    """Add the appropriate visual based on the type"""
    visual_mapping = {
        "Boonstra's veranderstrategieën": "Visual_1_Boonstra_Veranderstrategieen.png",
        "De Caluwé's kleurenmodel": "Visual_2_Caluwe_Kleurenmodel.png",
        "Gap-analyse model": "Visual_3_Gap_Analyse_Model.png",
        "Hofstede's cultuurdimensies": "Visual_4_Hofstede_Cultuurdimensies.png",
        "Kotter's 8-stappenmodel": "Visual_5_Kotter_8_Stappenmodel.png",
        "Stakeholderanalyse": "Visual_6_Stakeholderanalyse_Matrix.png",
        "Verandercurve van Kübler-Ross": "Visual_7_Kubler_Ross_Verandercurve.png",
        "Beslissingsmatrix Mintzberg": "Visual_8_Beslissingsmatrix_Mintzberg.png",
        "Boonstra's Beslissingsmatrix": "Visual_9_Boonstra_Beslissingsmatrix.png",
        "DMAIC-Kotter Integratie": "Visual_10_DMAIC_Kotter_Integratie.png"
    }
    
    filename = visual_mapping.get(visual_type)
    if filename and os.path.exists(filename):
        doc.add_picture(filename, width=Inches(6))
        print(f"Added visual: {filename}")
    else:
        print(f"Visual not found for: {visual_type}")

def get_caption_for_visual(visual_type):
    """Get the appropriate caption for the visual"""
    caption_mapping = {
        "Boonstra's veranderstrategieën": "Figuur 1: Boonstra's Veranderstrategieën - Karakteristieken",
        "De Caluwé's kleurenmodel": "Figuur 2: De Caluwé's Kleurenmodel - Denklogica's",
        "Gap-analyse model": "Figuur 3: Gap-Analyse Model",
        "Hofstede's cultuurdimensies": "Figuur 4: Hofstede's Cultuurdimensies - Euro Caps",
        "Kotter's 8-stappenmodel": "Figuur 5: Kotter's 8-Stappenmodel voor Verandering",
        "Stakeholderanalyse": "Figuur 6: Stakeholderanalyse - Power/Interest Matrix",
        "Verandercurve van Kübler-Ross": "Figuur 7: Verandercurve van Kübler-Ross",
        "Beslissingsmatrix Mintzberg": "Figuur 8: Beslissingsmatrix Organisatiestructuur (Mintzberg)",
        "Boonstra's Beslissingsmatrix": "Figuur 9: Beslissingsmatrix Veranderstrategie (Boonstra)",
        "DMAIC-Kotter Integratie": "Figuur 10: Integratie Six Sigma DMAIC met Kotter's 8-Stappenmodel"
    }
    
    return caption_mapping.get(visual_type, f"Figuur: {visual_type}")

if __name__ == "__main__":
    update_current_document() 