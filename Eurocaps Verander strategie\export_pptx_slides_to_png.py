import os
import win32com.client

# Pad naar de map met PowerPoints
pptx_folder = os.path.abspath(os.path.join(os.path.dirname(__file__), 'Bdk'))
output_folder = os.path.abspath(os.path.join(os.path.dirname(__file__), 'Exported_Slides'))

if not os.path.exists(output_folder):
    os.makedirs(output_folder)

# Start PowerPoint
ppt_app = win32com.client.Dispatch('PowerPoint.Application')
ppt_app.Visible = 1

for filename in os.listdir(pptx_folder):
    if filename.endswith('.pptx'):
        pptx_path = os.path.join(pptx_folder, filename)
        pres = ppt_app.Presentations.Open(pptx_path, WithWindow=False)
        # Submap per bestand
        pres_folder = os.path.join(output_folder, os.path.splitext(filename)[0])
        if not os.path.exists(pres_folder):
            os.makedirs(pres_folder)
        # Exporteer alle slides als PNG
        pres.SaveAs(pres_folder + "\\slide", 18)  # 18 = ppSaveAsPNG
        pres.Close()

ppt_app.Quit()

print(f"Alle slides geëxporteerd naar: {output_folder}") 