from docx import Document
import re

def merge_doc3_into_perfect():
    # Laad beide documenten
    doc_perfect = Document("Adviesrapport Veranderingsmanagement-<PERSON><PERSON>-1066741(perfect).docx")
    doc3 = Document("Adviesrapport Veranderingsmanagement-<PERSON><PERSON>-1066741(3).docx")

    # Maak een nieuwe doc als kopie van perfect
    final_doc = Document()
    for element in doc_perfect.element.body:
        final_doc.element.body.append(element)

    # Indexeer alle headings in perfect doc
    headings_map = {}
    for i, para in enumerate(final_doc.paragraphs):
        if para.style.name.startswith('Heading'):
            key = para.text.strip()
            headings_map[key] = i

    last_heading = None
    for para in doc3.paragraphs:
        if para.style.name.startswith('Heading'):
            last_heading = para.text.strip()
            continue
        # Voeg verdieping toe na elk hoofdstuk en subhoofdstuk
        if last_heading and last_heading in headings_map:
            if para.text.strip() and not para.text.strip().startswith('Tabel') and '[Visual' not in para.text:
                idx = headings_map[last_heading] + 1
                print(f"Voeg tekst toe na: {last_heading}")
                final_doc.paragraphs.insert(idx, para)
                for k in headings_map:
                    if headings_map[k] > headings_map[last_heading]:
                        headings_map[k] += 1

    # Opslaan als TEST_H2
    final_doc.save("Adviesrapport Veranderingsmanagement-Shuja Schadon-1066741(FINAAL).docx")
    print("Volledig document met verdieping uit (3) toegevoegd, tabellen en visuals behouden.")

if __name__ == "__main__":
    merge_doc3_into_perfect() 