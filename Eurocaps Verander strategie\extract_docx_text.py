import docx
import os
import sys

if len(sys.argv) < 2:
    print("Usage: python extract_docx_text.py <input_docx_path>")
    sys.exit(1)

docx_path = os.path.abspath(sys.argv[1])
output_txt = os.path.splitext(docx_path)[0] + "_Extracted.txt"

with open(docx_path, "rb") as f:
    doc = docx.Document(f)
    with open(output_txt, "w", encoding="utf-8") as out:
        for para in doc.paragraphs:
            out.write(para.text + "\n")

print(f"Transcript geëxporteerd naar {output_txt}")