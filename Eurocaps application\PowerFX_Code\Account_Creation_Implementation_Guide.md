# Account Creation Implementation Guide - EuroCaps Ordering System

## Overzicht
Deze guide legt uit hoe je de Account Creation functionaliteit toevoegt aan je PowerApps applicatie, inclusief de integratie met het bestaande login systeem.

## Stap 1: Nieuw Scherm Aanmaken

1. **Voeg een nieuw scherm toe** in PowerApps Studio
2. **Hernoem het scherm** naar `Account_Creation_Screen`
3. **Stel screen properties in:**
   - **Fill**: `RGBA(245, 245, 245, 1)`
   - **LoadingSpinnerColor**: `RGBA(74, 111, 165, 1)`

## Stap 2: OnVisible Event Account Creation Screen

**Selecteer Account_Creation_Screen** en voeg deze code toe aan **OnVisible**:

```powerfx
// Initialize form variables
Set(varUsernameValid, false);
Set(varEmailValid, false);
Set(varPasswordValid, false);
Set(varPasswordMatch, false);
Set(varFormValid, false);
Set(varUsernameAvailable, true);
Set(varEmailAvailable, true);

// Load existing user accounts for validation
ClearCollect(colUserAccounts,
    {UserID: 1, Username: "admin", Email: "<EMAIL>", FirstName: "System", LastName: "Administrator"},
    {UserID: 2, Username: "manager", Email: "<EMAIL>", FirstName: "Operations", LastName: "Manager"},
    {UserID: 3, Username: "sales", Email: "<EMAIL>", FirstName: "Sales", LastName: "Representative"},
    {UserID: 4, Username: "david_lee", Email: "<EMAIL>", FirstName: "David", LastName: "Lee"},
    {UserID: 5, Username: "john_smith", Email: "<EMAIL>", FirstName: "John", LastName: "Smith"}
);

// Initialize company types
ClearCollect(colCompanyTypes,
    {Value: "Retail", Text: "Retail"},
    {Value: "Wholesale", Text: "Wholesale"},
    {Value: "Restaurant", Text: "Restaurant"},
    {Value: "Office", Text: "Office"}
);

// Initialize countries
ClearCollect(colCountries,
    {Value: "Netherlands", Text: "Netherlands"},
    {Value: "Belgium", Text: "Belgium"},
    {Value: "Germany", Text: "Germany"},
    {Value: "France", Text: "France"}
)
```

## Stap 3: Form Controls Toevoegen

### Header Section
1. **Rectangle** voor header (Fill: `RGBA(74, 111, 165, 1)`)
2. **Image** voor logo
3. **Label** voor titel
4. **Button** voor "Back" (OnSelect: `Navigate(Login_Schreen, ScreenTransition.Fade)`)

### Form Container
1. **Rectangle** voor form container (Fill: `RGBA(255, 255, 255, 1)`)

### Username Field
1. **Label**: "Username: *"
2. **Text Input** met naam `UsernameInput`
3. **OnChange** code:
```powerfx
// Validate username format
Set(varUsernameValid, 
    Len(Self.Text) >= 3 && 
    Len(Self.Text) <= 20 && 
    IsMatch(Self.Text, "^[a-zA-Z0-9_]+$")
);

// Check username availability
Set(varUsernameAvailable, 
    IsBlank(LookUp(colUserAccounts, Username = Self.Text))
)
```
4. **Label** voor error message

### Email Field
1. **Label**: "Email: *"
2. **Text Input** met naam `EmailInput`
3. **OnChange** code:
```powerfx
// Validate email format
Set(varEmailValid, IsMatch(Self.Text, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"));

// Check email availability
Set(varEmailAvailable, 
    IsBlank(LookUp(colUserAccounts, Email = Self.Text))
)
```

### Password Fields
1. **Password Input** met naam `PasswordInput`
2. **OnChange** code:
```powerfx
// Validate password strength
Set(varPasswordValid, 
    Len(Self.Text) >= 8 && 
    IsMatch(Self.Text, "[A-Z]") &&  // Uppercase
    IsMatch(Self.Text, "[a-z]") &&  // Lowercase
    IsMatch(Self.Text, "[0-9]") &&  // Number
    IsMatch(Self.Text, "[!@#$%^&*(),.?\":{}|<>]")  // Special character
);

// Check password match
Set(varPasswordMatch, Self.Text = ConfirmPasswordInput.Text)
```

3. **Confirm Password Input** met naam `ConfirmPasswordInput`
4. **OnChange** code:
```powerfx
Set(varPasswordMatch, Self.Text = PasswordInput.Text)
```

### Personal Information
1. **FirstNameInput** - Text Input
2. **LastNameInput** - Text Input
3. **PhoneInput** - Text Input (optioneel)

### Company Information
1. **CompanyNameInput** - Text Input
2. **CompanyTypeDropdown** - Dropdown (Items: `colCompanyTypes`)

## Stap 4: Create Account Button

**Button** met naam `CreateAccountButton` en **OnSelect** code:

```powerfx
// Validate all required fields
If(
    IsBlank(UsernameInput.Text) || 
    IsBlank(EmailInput.Text) || 
    IsBlank(PasswordInput.Text) || 
    IsBlank(ConfirmPasswordInput.Text) ||
    IsBlank(FirstNameInput.Text) ||
    IsBlank(LastNameInput.Text) ||
    IsBlank(CompanyNameInput.Text),
    Notify("Please fill in all required fields", NotificationType.Error),
    
    // Check all validations
    If(
        !varUsernameValid || !varUsernameAvailable ||
        !varEmailValid || !varEmailAvailable ||
        !varPasswordValid || !varPasswordMatch,
        Notify("Please correct the errors in the form", NotificationType.Error),
        
        // Create new user account
        Patch(colUserAccounts, Defaults(colUserAccounts), {
            UserID: Max(colUserAccounts, UserID) + 1,
            Username: UsernameInput.Text,
            Email: EmailInput.Text,
            FirstName: FirstNameInput.Text,
            LastName: LastNameInput.Text,
            PhoneNumber: PhoneInput.Text,
            CompanyName: CompanyNameInput.Text,
            CompanyType: CompanyTypeDropdown.Selected.Value,
            AccountStatus: "Active",
            CreatedDate: Today(),
            EmailVerified: false,
            UserRole: "Customer"
        });
        
        // Show success message and navigate to login
        Notify("Account created successfully! Please log in with your new credentials.", NotificationType.Success);
        Navigate(Login_Schreen, ScreenTransition.Fade, {NewUsername: UsernameInput.Text})
    )
)
```

## Stap 5: Login Screen Aanpassingen

### Voeg Create Account Link toe aan Login_Schreen

1. **Button** met naam `CreateAccountLink`
2. **Text**: "Don't have an account? Create Account"
3. **OnSelect**: `Navigate(Account_Creation_Screen, ScreenTransition.Fade)`

### Update Login_Schreen OnVisible

Voeg deze code toe aan het einde van de bestaande OnVisible:

```powerfx
// Load user accounts from database (for newly created accounts)
If(
    !IsEmpty(colUserAccounts),
    ForAll(colUserAccounts,
        Collect(colStakeholders, {
            StakeholderID: UserID + 100,  // Offset to avoid conflicts
            Username: Username,
            Password: "password123",  // Default password for demo
            Role: UserRole,
            Name: FirstName & " " & LastName,
            AccessLevel: If(UserRole = "Customer", "Limited", "Full")
        })
    )
);

// Check if coming from account creation with new username
If(
    !IsBlank(Param("NewUsername")),
    Set(Hint_Username.Text, Param("NewUsername"));
    Notify("Account created successfully! Please enter your password to log in.", NotificationType.Success)
)
```

## Stap 6: Validatie Styling

### BorderColor voor Input Fields
Voor real-time visuele feedback, gebruik deze formule voor **BorderColor**:

```powerfx
If(
    !varFieldValid && !IsBlank(Self.Text),
    RGBA(231, 76, 60, 1),  // Red for invalid
    If(varFieldValid, RGBA(39, 174, 96, 1), RGBA(204, 204, 204, 1))  // Green for valid, gray for default
)
```

### Error Labels
Voor elke input field, voeg een **Label** toe voor error messages:

```powerfx
Text: =If(
    !IsBlank(InputField.Text) && !varFieldValid,
    "Error message here",
    ""
)
Visible: =!IsBlank(Self.Text)
Color: =RGBA(231, 76, 60, 1)  // Red
```

## Stap 7: Testing

### Test Scenario's:
1. **Username validatie**: Probeer korte/lange usernames, speciale karakters
2. **Email validatie**: Test ongeldige email formaten
3. **Password strength**: Test zwakke wachtwoorden
4. **Duplicate check**: Probeer bestaande usernames/emails
5. **Required fields**: Laat velden leeg en submit
6. **Successful creation**: Maak een geldig account aan

### Test Data:
- **Bestaande usernames**: admin, manager, sales, david_lee, john_smith
- **Bestaande emails**: <EMAIL>, <EMAIL>, etc.

## Stap 8: Productie Overwegingen

### Security:
- **Password hashing**: Implementeer proper password hashing
- **Email verification**: Voeg email verificatie toe
- **Rate limiting**: Voorkom spam account creation

### Database:
- **Persistent storage**: Connect naar echte database (SharePoint, SQL)
- **Backup**: Zorg voor data backup
- **Audit trail**: Log account creation events

### User Experience:
- **Email notifications**: Stuur welcome emails
- **Progressive disclosure**: Toon secties stapsgewijs
- **Mobile optimization**: Test op mobiele apparaten

## Troubleshooting

### Veelvoorkomende Problemen:
1. **Validatie werkt niet**: Check variabele namen en spelling
2. **Navigation faalt**: Controleer screen namen
3. **Collections leeg**: Check OnVisible events
4. **Styling problemen**: Controleer RGBA waarden

### Debug Tips:
- Gebruik **Monitor** tool voor debugging
- Check **Variables** tab voor variabele waarden
- Test **Collections** in **Data** tab
