# Logistix B.V. - Enterprise Architectuur Analyse
## Focusopdracht 1.4 - EA en APM

---

## Slide 1: Titel
**Logistix B.V. - Enterprise Architectuur Analyse**
- Focusopdracht 1.4 - EA en APM
- Casus: IT-landschap optimalisatie
- Datum: [Huidige datum]
- Student: [Naam]

---

## Slide 2: Bedrijfsprofiel Logistix B.V.

### Over het bedrijf:
- **Sector**: Logistieke dienstverlening
- **Specialisatie**: E-fulfilment en opslag- & distributieoplossingen
- **Doelgroep**: Webshops (grote en kleine klanten)
- **Probleem**: Gebrek aan synergie tussen systemen
- **Doel**: IT-landschap optimaliseren vanwege toegenomen complexiteit

### Huidige situatie:
- Verouderde systemen met beperkte integratie
- Handmatige processen leiden tot fouten en vertragingen
- Operationele problemen door gebrek aan automatisering

---

## Slide 3: Bedrijfsprocessen - Orderverwerking

### Huidige orderverwerking:
1. **Order ontvangst**:
   - Grote klanten: Verouderde API-koppeling
   - Kleine klanten: E-mail of batchbestanden

2. **Dubbele handmatige invoer**:
   - Ordergegevens invoeren in ERP-systeem
   - Dezelfde gegevens opnieuw invoeren in WMS
   - **Gevolg**: Veel fouten en vertraging

3. **Losgekoppelde validatie**:
   - ERP controleert klantgegevens
   - WMS valideert voorraad
   - Handmatig schakelen tussen systemen

---

## Slide 4: Bedrijfsprocessen - Magazijnbeheer

### Picking & Packing:
- WMS genereert picklijst
- **Probleem**: Handmatige opzoek van vervoerder info
- **Probleem**: Handmatige invoer verzendlabels in TMS
- Geen directe koppeling tussen picking en verzending

### Verzending:
- **Probleem**: Handmatig kopiëren van trackingnummers
- Van TMS naar ERP voor facturatie
- Van TMS naar ERP voor klantcommunicatie
- **Gevolg**: Tijdrovend en foutgevoelig

---

## Slide 5: Bedrijfsprocessen - Klantenservice

### Huidige situatie:
- CRM-systeem staat los van alle andere applicaties
- **Probleem**: Geen automatische synchronisatie van order/status info

### Voor elke klantvraag:
1. Order opzoeken in ERP-systeem
2. Status opzoeken in TMS
3. Handmatig samenvoegen van informatie
4. **Gevolg**: Lange responstijd voor klanten

---

## Slide 6: Belangrijkste Knelpunten

### 1. **Handmatige dubbele invoer**
- Orders moeten in zowel ERP als WMS ingevoerd worden
- Verhoogd risico op fouten en inconsistenties

### 2. **Gebrek aan integratie**
- Systemen staan los van elkaar
- Geen automatische data-uitwisseling
- Handmatig schakelen tussen systemen

### 3. **Verouderde technologie**
- WMS heeft beperkte integratiemogelijkheden
- API-koppelingen zijn verouderd
- Kostbare uitbreidingen

### 4. **Inefficiënte processen**
- Handmatig kopiëren van trackingnummers
- Handmatige opzoek van verzendinfo
- Lange responstijden klantenservice

### 5. **Data inconsistentie**
- Door handmatige invoer niet altijd accurate data
- ERP is bedoeld als 'bron van waarheid' maar data klopt niet altijd

---

## Slide 7: Applicatielandschap - Huidige Situatie

### **WMS (Warehouse Management System)**
- **Type**: On-premise systeem
- **Functie**: Magazijnprocessen aansturen
- **Probleem**: Oudste applicatie, beperkte integratie, kostbare uitbreidingen

### **ERP (Enterprise Resource Planning)**
- **Type**: Cloud-gebaseerd systeem
- **Functie**: Financiën, facturatie, klantbeheer
- **Probleem**: Data niet altijd accuraat door handmatige invoer

### **TMS (Transport Management System)**
- **Type**: Standalone softwarepakket
- **Functie**: Verzendlabels, communicatie vervoerders
- **Probleem**: Handmatige data-uitwisseling met WMS en ERP

### **CRM (Customer Relationship Management)**
- **Type**: Losstaand systeem
- **Functie**: Klantcontact en klachten registratie
- **Probleem**: Geen automatische synchronisatie van order/status info

---

## Slide 8: ArchiMate Architectuur Screenshot

**[HIER KOMT DE SCREENSHOT VAN HET ARCHIMATE DIAGRAM]**

### Architectuur toont:
- **Business Layer**: Actoren, processen en services
- **Application Layer**: Systemen en hun onderlinge relaties
- **Technology Layer**: Infrastructuur en platforms

### Belangrijke observaties:
- Veel handmatige stappen (rood gemarkeerd)
- Beperkte automatische koppelingen
- Geïsoleerde systemen zonder integratie

---

## Slide 9: Verbetervoorstel 1 - Integratie Platform (APM)

### **Implementatie van Enterprise Service Bus (ESB)**

**Probleem aangepakt**: Gebrek aan integratie tussen systemen

**Oplossing**:
- Centraal integratieplatform tussen alle systemen
- Real-time data synchronisatie
- Eliminatie van handmatige dubbele invoer

**Voordelen**:
- Automatische orderverwerking van ontvangst tot ERP/WMS
- Consistente data tussen alle systemen
- Verminderde fouten en verwerkingstijd

**APM Classificatie**: **INVEST** - Strategische verbetering voor toekomst
**Prioriteit**: Hoog - Lost meerdere kernproblemen op

---

## Slide 10: Verbetervoorstel 2 - API Modernisering (APM)

### **Vervanging Verouderde API & Nieuwe Koppelingen**

**Probleem aangepakt**: Verouderde API en handmatige processen

**Oplossing**:
- Moderne REST API's voor klantorders
- Directe koppeling WMS ↔ TMS voor verzendlabels
- Automatische tracking synchronisatie TMS ↔ ERP

**Voordelen**:
- Geautomatiseerde verzendlabel generatie
- Automatische tracking updates
- Snellere orderverwerking

**APM Classificatie**: **MIGRATE** - Vervanging verouderde technologie
**Prioriteit**: Hoog - Kritiek voor operationele efficiëntie

---

## Slide 11: Verbetervoorstel 3 - Customer Portal & CRM Integratie (APM)

### **Geïntegreerd Klantportaal met Real-time Status**

**Probleem aangepakt**: Inefficiënte klantenservice en handmatige status opzoek

**Oplossing**:
- Self-service klantportaal met real-time order tracking
- CRM integratie met ERP en TMS voor complete klanthistorie
- Geautomatiseerde status updates naar klanten

**Voordelen**:
- Verminderde belasting klantenservice
- Betere klantervaring door transparantie
- Snellere probleemoplossing door geïntegreerde data

**APM Classificatie**: **ENHANCE** - Verbetering klantervaring
**Prioriteit**: Medium - Toegevoegde waarde na basis integraties

---

## Slide 12: Implementatie Roadmap

### **Fase 1 (0-6 maanden): Basis Integratie**
- ESB implementatie tussen ERP en WMS
- Eliminatie dubbele orderinvoer
- **Quick win**: Directe ROI door tijdsbesparing

### **Fase 2 (6-12 maanden): API Modernisering**
- Nieuwe klant API's
- WMS-TMS koppeling voor verzendlabels
- TMS-ERP koppeling voor tracking

### **Fase 3 (12-18 maanden): Customer Experience**
- CRM integratie met alle systemen
- Klantportaal ontwikkeling
- Self-service mogelijkheden

### **Verwachte Resultaten**:
- 60% reductie handmatige taken
- 40% snellere orderverwerking
- 80% reductie klantenservice vragen over status

---

## Slide 13: Conclusie & Aanbevelingen

### **Huidige Situatie**:
- Versnipperd IT-landschap met veel handmatige processen
- Operationele inefficiënties en verhoogd foutenrisico
- Slechte klantervaring door trage responstijden

### **Aanbevolen Aanpak**:
1. **Start met ESB implementatie** - Grootste impact op operationele efficiëntie
2. **Moderniseer API's gefaseerd** - Vermijd big-bang implementatie
3. **Investeer in klantervaring** - Concurrentievoordeel op lange termijn

### **Kritieke Succesfactoren**:
- Management commitment voor gefaseerde aanpak
- Training medewerkers op nieuwe processen
- Monitoring en continue optimalisatie

### **Verwachte ROI**: 18-24 maanden door besparingen op personeelskosten en verhoogde klanttevredenheid

---

## Slide 14: Vragen & Discussie

**Bedankt voor uw aandacht!**

### Discussiepunten:
- Prioritering van de voorgestelde verbeteringen
- Budget en resource allocatie
- Timing van implementatie fasen
- Change management aanpak

### Contact:
- [Naam student]
- [Email]
- [Datum presentatie]
