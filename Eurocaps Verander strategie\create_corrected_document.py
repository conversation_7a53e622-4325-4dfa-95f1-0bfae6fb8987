import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor

def create_document():
    """Create document with proper formatting"""
    
    doc = Document()
    
    # Set default styles
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Arial'
    font.size = Pt(12)
    
    # Title page
    title = doc.add_heading('Adviesrapport Veranderingsmanagement:', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitle = doc.add_paragraph('Optimalisatie van Euro Caps door Gerichte Structuur- en Cultuurverandering met Six Sigma')
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Document info
    doc.add_paragraph()
    doc.add_paragraph('Versie: 2')
    doc.add_paragraph('Naam van organisatie en opleiding: Hogeschool Rotterdam BIM')
    doc.add_paragraph('Naam: <PERSON><PERSON>')
    doc.add_paragraph('Studentennummer: 1066471')
    doc.add_paragraph('Onderwijsperiode: OP4')
    doc.add_paragraph('Plaats en datum: Rotterdam 03-07-2025')
    doc.add_paragraph('Docenten: <PERSON>lug, Aicha Manuela Martijn')
    
    # Management Summary on separate page
    doc.add_page_break()
    doc.add_heading('Managementsamenvatting', 1)
    
    summary_text = """Dit adviesrapport presenteert een integrale strategie voor Euro Caps om de organisatiestructuur en -cultuur te optimaliseren, voortbouwend op de reeds geïmplementeerde Six Sigma methodiek. Het rapport begint met een inleiding waarin de achtergrond en de noodzaak van deze veranderingen worden geschetst, waarbij de focus ligt op het versterken van de organisatie naast de procesverbeteringen die door Six Sigma worden gerealiseerd.

Het theoretisch kader in Hoofdstuk 2 licht de fundamenten van verandermanagement toe, waaronder strategieën van Boonstra (2018), de veranderkleuren van De Caluwé en Vermaak (2009), de gap-analyse, Hofstede's cultuurdimensies (Hofstede, Hofstede & Minkov, 2010), Kotter's achtstappenmodel (Kotter, 1996), stakeholderanalyse en de verandercurve van Kübler-Ross (1969).

Hoofdstuk 3 biedt een grondige analyse van de huidige organisatiestructuur en -cultuur van Euro Caps en identificeert concrete knelpunten die de implementatie van Six Sigma belemmeren. Vervolgens schetst Hoofdstuk 4 de gewenste situatie, met een focus op een flexibelere structuur en een meer mensgerichte, lerende cultuur, essentieel voor continue kwaliteitsverbetering.

De kern van dit rapport, Hoofdstuk 5, ontvouwt een gedetailleerde veranderstrategie en implementatieplan volgens Kotter's achtstappenmodel, waarbij Six Sigma's DMAIC-cyclus een integrale rol speelt in de operationele uitvoering over een periode van 21 maanden. Dit omvat ook een uitgebreide stakeholdersanalyse en de aanpak van mogelijke weerstanden op basis van Kübler-Ross.

Tenslotte beschrijft Hoofdstuk 6 een concreet communicatieplan, afgestemd op de mensbeelden van De Caluwé, hoe de boodschap effectief wordt overgebracht aan alle betrokkenen. De conclusie in Hoofdstuk 7 vat de bevindingen samen en mondt uit in concrete aanbevelingen die Euro Caps in staat stellen een duurzaam competitieve en lerende organisatie te worden."""
    
    doc.add_paragraph(summary_text)
    
    # Foreword on separate page
    doc.add_page_break()
    doc.add_heading('Voorwoord', 1)
    
    foreword_text = """Dit rapport is opgesteld in opdracht van Euro Caps met als doel een strategisch kader te bieden voor effectief veranderingsmanagement. De aanbevelingen zijn gebaseerd op grondige analyse van organisatiestructuur, bedrijfscultuur en operationele processen, voortbouwend op de keuze voor Six Sigma uit een eerder onderzoek.

Wij danken in het bijzonder onze docenten, Robert Vlug en Aicha Manuela Martijn, voor hun waardevolle begeleiding en feedback gedurende dit traject. Tevens danken wij alle betrokken stakeholders voor hun input die essentieel was voor de diepgang van dit advies."""
    
    doc.add_paragraph(foreword_text)
    
    # Table of Contents on separate page
    doc.add_page_break()
    doc.add_heading('Inhoudsopgave', 1)
    
    toc_items = [
        "Managementsamenvatting",
        "Voorwoord",
        "",
        "Hoofdstuk 1: Inleiding",
        "    1.1 Deskresearch methode",
        "    1.2 Leeswijzer",
        "Hoofdstuk 2: Theoretisch kader",
        "    2.1 Veranderstrategieën volgens Boonstra",
        "    2.2 Veranderkleuren van De Caluwé",
        "    2.3 Gap-analyse & Hofstede-model",
        "    2.4 Kotter's 8 Stappenmodel",
        "    2.5 Stakeholderanalyse",
        "    2.6 Verandercurve van Kübler-Ross",
        "Hoofdstuk 3: Huidige situatie",
        "    3.1 Huidige organisatiestructuur",
        "    3.2 Huidige organisatiecultuur",
        "    3.3 Deelconclusie beantwoorden",
        "Hoofdstuk 4: Gewenste situatie",
        "    4.1 Gewenste organisatiestructuur",
        "    4.2 Gewenste organisatiecultuur",
        "    4.3 Deelconclusie beantwoorden",
        "Hoofdstuk 5: Veranderstrategie + implementatieplan",
        "    5.1 Voorbereidende deel",
        "        5.1.1 Organisatiestructuur veranderingen",
        "        5.1.2 Organisatiecultuur veranderingen",
        "        5.1.3 Stakeholdersanalyse",
        "        5.1.4 Mogelijke weerstanden van Kübler-Ross",
        "    5.2 Uitvoerende deel",
        "        5.2.1 Strategische veranderaanpak",
        "        5.2.2 Veranderstrategie Boonstra",
        "        5.2.3 Veranderaanpak Kotter",
        "        5.2.4 Interventies van de stakeholder",
        "    5.3 Deelconclusie beantwoorden",
        "Hoofdstuk 6: Communicatieplan",
        "    6.1 Overzicht communicatieplan",
        "Hoofdstuk 7: Conclusie",
        "Aanbeveling",
        "Literatuurlijst",
        "Argumentatieschema",
        "Bijlage"
    ]
    
    for item in toc_items:
        if item:
            doc.add_paragraph(item)
        else:
            doc.add_paragraph()
    
    return doc

def add_chapter_1(doc):
    """Add Chapter 1 on separate page"""
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 1: Inleiding', 1)
    
    intro_text = """Dit inleidende hoofdstuk schetst de achtergrond en het doel van dit adviesrapport. Het bouwt voort op eerdere bevindingen en aanbevelingen met betrekking tot kwaliteitsmanagement binnen Euro Caps, specifiek de reeds vastgestelde keuze voor de Six Sigma methodiek met het DMAIC-raamwerk. Er wordt ingegaan op de noodzaak om, naast procesoptimalisatie, ook de organisatiestructuur en -cultuur te analyseren en waar nodig aan te passen om de effectiviteit van Six Sigma te maximaliseren en duurzame verbetering te garanderen.

Euro Caps staat voor belangrijke operationele en organisatorische uitdagingen die vragen om een gestructureerde veranderingsaanpak. Dit rapport biedt een analyse van de huidige situatie, een onderbouwd actieplan en een visie op de gewenste toekomstige staat. De aanbevelingen combineren bewezen methodieken uit veranderingsmanagement en procesoptimalisatie, afgestemd op de specifieke context van Euro Caps.

De centrale vraag die in dit rapport wordt beantwoord, luidt: "Hoe kan Euro Caps op een effectieve en duurzame manier de organisatie inrichten en de cultuur ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma?"

Het doel van dit rapport is om een onderbouwd veranderplan te formuleren en een praktisch uitvoerbaar plan op te stellen waarmee Euro Caps de organisatorische transitie succesvol kan realiseren. Hierbij worden zowel interne als externe factoren in kaart gebracht en vertaald naar een concreet actie-, communicatie- en implementatieplan.

Om tot een volledig en bruikbaar advies te komen, worden de volgende deelvragen behandeld:
1. Welke veranderstrategie sluit het beste aan bij de organisatiekenmerken van Euro Caps om de Six Sigma implementatie te optimaliseren en duurzaam te verankeren?
2. Op welke wijze kan draagvlak worden gecreëerd bij interne en externe stakeholders voor de structurele en culturele veranderingen die nodig zijn ter ondersteuning van continue kwaliteitsverbetering?"""
    
    doc.add_paragraph(intro_text)
    
    # 1.1 Deskresearch methode
    doc.add_heading('1.1 Deskresearch methode', 2)
    method_text = """De methode van onderzoek bestaat primair uit deskresearch, waarbij gebruik is gemaakt van relevante literatuur en theorieën op het gebied van organisatiestructuur, organisatiecultuur, verandermanagement, communicatie en stakeholdermanagement. Specifiek zijn theorieën van Mintzberg (1983) over coördinatiemechanismen en Hofstede, Hofstede en Minkov (2010) cultuurdimensies voor organisaties toegepast om de huidige en gewenste situatie te analyseren. Voor de veranderstrategie is geput uit de BDK-theorie van Boonstra (2018) en het achtstappenmodel van Kotter (1996), aangevuld met communicatieprincipes gebaseerd op de mensbeelden van De Caluwé en Vermaak (2009). De theorie van Kübler-Ross (1969) over de fasen van rouw is ingezet voor de analyse van mogelijke weerstanden."""
    doc.add_paragraph(method_text)
    
    # 1.2 Leeswijzer
    doc.add_heading('1.2 Leeswijzer', 2)
    reader_guide = """Dit adviesrapport is als volgt gestructureerd: Hoofdstuk 2 presenteert het theoretisch kader dat de basis vormt voor de analyse. Hoofdstuk 3 beschrijft de huidige situatie van Euro Caps, met aandacht voor de organisatiestructuur en -cultuur. Hoofdstuk 4 schetst de gewenste situatie, eveneens op het vlak van structuur en cultuur. Hoofdstuk 5 formuleert de veranderstrategie en het implementatieplan, inclusief een stakeholdersanalyse en een inschatting van mogelijke weerstanden. Hoofdstuk 6 detailleert het communicatieplan. Ten slotte biedt Hoofdstuk 7 de conclusie en concrete aanbevelingen voor Euro Caps."""
    doc.add_paragraph(reader_guide)
    
    return doc

if __name__ == "__main__":
    print("=== Creating Corrected Document ===")
    doc = create_document()
    doc = add_chapter_1(doc)
    
    # Save document
    doc.save('Adviesrapport_Veranderingsmanagement_CORRECTED_BASE.docx')
    print("Base document created successfully!")
