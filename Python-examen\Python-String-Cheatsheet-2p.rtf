{\rtf1\ansi\deff0
{\fonttbl{\f0 Arial;}}
\fs16
\b Python Cheat Sheet – Pagina 1 (Alle modules: 1 regel + mini-uitleg)\b0\line
Let op: piepklein font; elk voorbeeld is 1 regel met mini-uitleg.\line
\line
\b String-methoden\b0\line
capitalize  \tab print([w.capitalize() for w in ["hallo","wereld"]])   \tab Eerste letter hoofdletter\line
casefold    \tab print([w.casefold() for w in ["Straße","HELLO"]])     \tab Extra sterke lower\line
center      \tab print([w.center(6,'-') for w in ["hi","ok"]])          \tab Uitvullen midden\line
count       \tab print([w.count('na') for w in ["banana","ananas"]])     \tab Aantal keer sub\line
encode      \tab print([w.encode('utf-8') for w in ["café","naïve"]])     \tab Naar bytes\line
endswith    \tab print([w.endswith('.py') for w in ["a.py","b.txt"]])     \tab Eindigt op\line
expandtabs  \tab print([w.expandtabs(4) for w in ["a\tb","1\t2"]])       \tab Tabs → spaties\line
find        \tab print([w.find('na') for w in ["banana","ananas"]])       \tab Eerste index of -1\line
format      \tab print(["Hoi {}".format(x) for x in ["Piet","Klaas"]])    \tab Vullen placeholders\line
format_map  \tab print(["{x}-{y}".format_map(m) for m in [{\'27}x\'3a1,\'27}y\'3a2},{\'27}x\'3a3,\'27}y\'3a4}]])  \tab Vullen met dict\line
index       \tab print([w.index('na') for w in ["banana","ananas"]])      \tab Eerste index of error\line
isalnum     \tab print([w.isalnum() for w in ["abc123","abc!"]])          \tab Alleen letters/cijfers\line
isalpha     \tab print([w.isalpha() for w in ["abc","abc1"]])             \tab Alleen letters\line
isascii     \tab print([w.isascii() for w in ["abc","é"]])                \tab ASCII?\line
isdecimal   \tab print([w.isdecimal() for w in ["10","½"]])               \tab Decimale digits\line
isdigit     \tab print([w.isdigit() for w in ["10","²"]])                 \tab Digits (incl. super)\line
isidentifier\tab print([w.isidentifier() for w in ["var_1","1abc"]])      \tab Geldige naam?\line
islower     \tab print([w.islower() for w in ["abc","Abc"]])              \tab Alles lower?\line
isnumeric   \tab print([w.isnumeric() for w in ["10","½"]])               \tab Numeriek (breuken)\line
isprintable \tab print([w.isprintable() for w in ["abc","\n"]])           \tab Printable?\line
isspace     \tab print([w.isspace() for w in ["   ","a"]])                 \tab Alleen whitespace\line
istitle     \tab print([w.istitle() for w in ["Hello World","Hello world"]]) \tab Titelcase?\line
isupper     \tab print([w.isupper() for w in ["ABC","AbC"]])              \tab Alles upper?\line
join        \tab print('-'.join(["a","b","c"]))                         \tab Voeg lijst samen\line
ljust       \tab print([w.ljust(5,'.') for w in ["hi","ok"]])             \tab Links uitvullen\line
lower       \tab print([w.lower() for w in ["AbC","XYZ"]])                \tab Naar lowercase\line
lstrip      \tab print([w.lstrip() for w in ["  hi","\tok"]])            \tab Links trimmen\line
maketrans   \tab print("eat".translate(str.maketrans('ea','3@')))           \tab Translatietabel\line
partition   \tab print([w.partition('-') for w in ["a-b-c","1-2-3"]])      \tab Split in 3 (links,sep,rechts)\line
replace     \tab print([w.replace('l','x',1) for w in ["hello","ball"]])   \tab Vervang sub\line
rfind       \tab print([w.rfind('na') for w in ["banana","ananas"]])       \tab Laatste index of -1\line
rindex      \tab print([w.rindex('na') for w in ["banana","ananas"]])      \tab Laatste index of error\line
rjust       \tab print([w.rjust(5,'.') for w in ["hi","ok"]])             \tab Rechts uitvullen\line
rpartition  \tab print([w.rpartition('-') for w in ["a-b-c","1-2-3"]])     \tab Splits vanaf rechts\line
rsplit      \tab print([w.rsplit(None,1) for w in ["a b c","1 2 3"]])     \tab Rechts splitsen\line
rstrip      \tab print([w.rstrip() for w in ["hi  ","ok..."]])            \tab Rechts trimmen\line
split       \tab print([w.split(',') for w in ["a,b,c","1,2,3"]])         \tab Splits op sep\line
splitlines  \tab print([w.splitlines() for w in ["a\nb","x\ny"]])         \tab Splits op \linebreaks\line
startswith  \tab print([w.startswith('he') for w in ["hello","world"]])    \tab Begint met\line
strip       \tab print([w.strip(' .') for w in ["  hi  ","..ok.."]])        \tab Trim links+rechts\line
swapcase    \tab print([w.swapcase() for w in ["AbC","xYz"]])             \tab Wissel case\line
title       \tab print([w.title() for w in ["hello world","good-day"]])   \tab Woorden Titelcase\line
translate   \tab print([w.translate(str.maketrans({'e':'3','a':'@'})) for w in ["eat","bee"]]) \tab Met tabel\line
upper       \tab print([w.upper() for w in ["abc","xYz"]])                \tab Naar uppercase\line
zfill       \tab print([w.zfill(4) for w in ["7","42"]])                  \tab Voorloopnullen\line
\line
\b List-methoden\b0\line
append      \tab lst=["a"]; lst.append("b"); print(lst)                    \tab Voeg element achteraan\line
extend      \tab lst=["a"]; lst.extend(["b","c"]); print(lst)             \tab Lijst achteraan toevoegen\line
insert      \tab lst=["a","c"]; lst.insert(1,"b"); print(lst)            \tab Invoegen op index\line
remove      \tab lst=["a","b","a"]; lst.remove("a"); print(lst)          \tab Verwijder eerste voorkomen\line
pop         \tab lst=["a","b","c"]; print(lst.pop(1), lst)               \tab Verwijder op index + return\line
del         \tab lst=["a","b","c"]; del lst[0:2]; print(lst)             \tab Del element of slice\line
index       \tab lst=["a","b","a"]; print(lst.index("a"))               \tab Index eerste voorkomen\line
count       \tab lst=["a","b","a"]; print(lst.count("a"))               \tab Aantal voorkomen\line
sort        \tab lst=[3,1,2]; lst.sort(); print(lst)                         \tab Sorteer in-place\line
reverse     \tab lst=[1,2,3]; lst.reverse(); print(lst)                      \tab Keer volgorde om\line
\page
\fs16
\b Pagina 2 – Uitgebreide uitleg + compacte code\b0\line
- Patroon voor string-methoden:\line
text=["a","b"]; res=[w.METHODE(args) for w in text]; print(res)\line
- Niet per-word (bijv. join/encode): gebruik direct op string of lijst.\line
\line
\b String-methoden (2 per blok, 3 regels)\b0\line
text=["hallo","wereld"]; cap=[w.capitalize() for w in text]\line
low=[w.lower() for w in text]\line
print(cap, low)\line
text=["Straße","HELLO"]; cf=[w.casefold() for w in text]\line
up=[w.upper() for w in text]\line
print(cf, up)\line
text=["hi","ok"]; cen=[w.center(6,'-') for w in text]\line
lj=[w.ljust(5,'.') for w in text]\line
print(cen, lj)\line
text=["hi","ok"]; rj=[w.rjust(5,'.') for w in text]\line
sw=[w.swapcase() for w in ["AbC","xYz"]]\line
print(rj, sw)\line
text=["banana","ananas"]; c=[w.count('na') for w in text]\line
fi=[w.find('na') for w in text]\line
print(c, fi)\line
text=["banana","ananas"]; rfi=[w.rfind('na') for w in text]\line
ix=[w.index('na') for w in text]\line
print(rfi, ix)\line
text=["banana","ananas"]; rix=[w.rindex('na') for w in text]\line
st=[w.startswith('ba') for w in text]\line
print(rix, st)\line
text=["hello","world"]; en=[w.endswith('ld') for w in text]\line
tt=[w.title() for w in ["hello world","good-day"]]\line
print(en, tt)\line
text=["  hi  ","..ok.."] ; s=[w.strip(' .') for w in text]\line
ls=[w.lstrip() for w in ["  hi","\tok"]]\line
print(s, ls)\line
text=["hi  ","ok..."] ; rs=[w.rstrip() for w in text]\line
sl=[w.split(',') for w in ["a,b,c","1,2,3"]]\line
print(rs, sl)\line
text=["a b c","1 2 3"]; rsl=[w.rsplit(None,1) for w in text]\line
sll=[w.splitlines() for w in ["a\nb","x\ny"]]\line
print(rsl, sll)\line
text=["abc","xYz"]; isup=[w.isupper() for w in ["ABC","AbC"]]\line
isl=[w.islower() for w in ["abc","Abc"]]\line
print(isup, isl)\line
text=["abc","\n"]; isp=[w.isprintable() for w in text]\line
iss=[w.isspace() for w in ["   ","a"]]\line
print(isp, iss)\line
text=["10","½"]; isd=[w.isdecimal() for w in ["10","12"]]\line
isn=[w.isnumeric() for w in ["10","½"]]\line
print(isd, isn)\line
text=["var_1","1abc"]; iid=[w.isidentifier() for w in text]\line
ias=[w.isascii() for w in ["abc","é"]]\line
print(iid, ias)\line
print("-".join(["a","b","c"]))\line
print("eat".translate(str.maketrans('ea','3@')))\line
print([w.encode('utf-8') for w in ["café","naïve"]])\line
print([w.zfill(4) for w in ["7","42"]])\line
\line
\b List-methoden (3 regels per methode)\b0\line
lst=["a"]; lst.append("b"); print(lst)\line
lst=["a"]; lst.extend(["b","c"]); print(lst)\line
lst=["a","c"]; lst.insert(1,"b"); print(lst)\line
lst=["a","b","a"]; lst.remove("a"); print(lst)\line
lst=["a","b","c"]; print(lst.pop(1), lst)\line
lst=["a","b","c"]; del lst[0:2]; print(lst)\line
lst=["a","b","a"]; print(lst.index("a"))\line
lst=["a","b","a"]; print(lst.count("a"))\line
lst=[3,1,2]; lst.sort(); print(lst)\line
lst=[1,2,3]; lst.reverse(); print(lst)\line
}

