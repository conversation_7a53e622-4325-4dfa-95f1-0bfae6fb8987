import os
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn

def create_complete_document():
    """
    Create a complete document integrating all content from document (3) 
    with all visual indicators and missing content
    """
    
    # Create new document
    doc = Document()
    
    # Set document title
    title = doc.add_heading('Adviesrapport Veranderingsmanagement:', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitle = doc.add_heading('Optimalisatie van Euro Caps door Gerichte Structuur- en Cultuurverandering met Six Sigma', 1)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add document info
    doc.add_paragraph('Versie: 2')
    doc.add_paragraph('Naam van organisatie en opleiding: Hogeschool Rotterdam BIM')
    doc.add_paragraph('Naam: <PERSON><PERSON>')
    doc.add_paragraph('Studentennummer: 1066471')
    doc.add_paragraph('Onderwijsperiode: OP4')
    doc.add_paragraph('Plaats en datum: Rotterdam 03-07-2025')
    doc.add_paragraph('Docenten: <PERSON>, <PERSON><PERSON>')
    
    # Add Management Summary
    doc.add_heading('Managementsamenvatting', 1)
    management_summary = """Dit adviesrapport presenteert een integrale strategie voor Euro Caps om de organisatiestructuur en -cultuur te optimaliseren, voortbouwend op de reeds geïmplementeerde Six Sigma methodiek. Het rapport begint met een inleiding waarin de achtergrond en de noodzaak van deze veranderingen worden geschetst, waarbij de focus ligt op het versterken van de organisatie naast de procesverbeteringen die door Six Sigma worden gerealiseerd. Het theoretisch kader in Hoofdstuk 2 licht de fundamenten van verandermanagement toe, waaronder strategieën van Boonstra, de veranderkleuren van De Caluwé, de gap-analyse, Hofstede's cultuurdimensies, Kotter's achtstappenmodel, stakeholderanalyse en de verandercurve van Kübler-Ross. Hoofdstuk 3 biedt een grondige analyse van de huidige organisatiestructuur en -cultuur van Euro Caps en identificeert concrete knelpunten die de implementatie van Six Sigma belemmeren. Vervolgens schetst Hoofdstuk 4 de gewenste situatie, met een focus op een flexibelere structuur en een meer mensgerichte, lerende cultuur, essentieel voor continue kwaliteitsverbetering. De kern van dit rapport, Hoofdstuk 5, ontvouwt een gedetailleerde veranderstrategie en implementatieplan volgens Kotter's achtstappenmodel, waarbij Six Sigma's DMAIC-cyclus een integrale rol speelt in de operationele uitvoering over een periode van 21 maanden. Dit omvat ook een uitgebreide stakeholdersanalyse en de aanpak van mogelijke weerstanden op basis van Kübler-Ross. Tenslotte beschrijft Hoofdstuk 6 een concreet communicatieplan, afgestemd op de mensbeelden van De Caluwé, hoe de boodschap effectief wordt overgebracht aan alle betrokkenen. De conclusie in Hoofdstuk 7 vat de bevindingen samen en mondt uit in concrete aanbevelingen die Euro Caps in staat stellen een duurzaam competitieve en lerende organisatie te worden."""
    doc.add_paragraph(management_summary)
    
    # Add Foreword
    doc.add_heading('Voorwoord', 1)
    foreword = """Dit rapport is opgesteld in opdracht van Euro Caps met als doel een strategisch kader te bieden voor effectief veranderingsmanagement. De aanbevelingen zijn gebaseerd op grondige analyse van organisatiestructuur, bedrijfscultuur en operationele processen, voortbouwend op de keuze voor Six Sigma uit een eerder onderzoek. Wij danken in het bijzonder onze docenten, Robert Vlug en Aicha Manuela Martijn, voor hun waardevolle begeleiding en feedback gedurende dit traject. Tevens danken wij alle betrokken stakeholders voor hun input die essentieel was voor de diepgang van dit advies."""
    doc.add_paragraph(foreword)
    
    # Add Table of Contents
    doc.add_heading('Inhoudsopgave', 1)
    toc_content = """
Managementsamenvatting
Voorwoord

• Hoofdstuk 1: Inleiding
    • 1.1 Deskresearch methode
    • 1.2 Leeswijzer
• Hoofdstuk 2: Theoretisch kader
    • 2.1 Veranderstrategieën volgens Boonstra
    • 2.2 Veranderkleuren van De Caluwé
    • 2.3 Gap-analyse & Hofstede-model
    • 2.4 Kotter's 8 Stappenmodel
    • 2.5 Stakeholderanalyse
    • 2.6 Verandercurve van Kübler-Ross
• Hoofdstuk 3: Huidige situatie
    • 3.1 Huidige organisatiestructuur
    • 3.2 Huidige organisatiecultuur
    • 3.3 Deelconclusie beantwoorden
• Hoofdstuk 4: Gewenste situatie
    • 4.1 Gewenste organisatiestructuur
    • 4.2 Gewenste organisatiecultuur
    • 4.3 Deelconclusie beantwoorden
• Hoofdstuk 5: Veranderstrategie + implementatieplan
    • 5.1 Voorbereidende deel
        • 5.1.1 Organisatiestructuur veranderingen
        • 5.1.2 Organisatiecultuur veranderingen
        • 5.1.3 Stakeholdersanalyse
        • 5.1.4 Mogelijke weerstanden van Kübler-Ross
    • 5.2 Uitvoerende deel
        • 5.2.1 Strategische veranderaanpak
        • 5.2.2 Veranderstrategie Boonstra
        • 5.2.3 Veranderaanpak Kotter
        • 5.2.4 Interventies van de stakeholder
    • 5.3 Deelconclusie beantwoorden
• Hoofdstuk 6: Communicatieplan
    • 6.1 Overzicht communicatieplan
• Hoofdstuk 7: Conclusie
• Aanbeveling
• Literatuurlijst
• Argumentatieschema
• Bijlage
"""
    doc.add_paragraph(toc_content)
    
    return doc

def add_chapter_1(doc):
    """Add Chapter 1: Introduction with all content from document (3)"""
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 1: Inleiding', 1)
    
    intro_text = """Dit inleidende hoofdstuk schetst de achtergrond en het doel van dit adviesrapport. Het bouwt voort op eerdere bevindingen en aanbevelingen met betrekking tot kwaliteitsmanagement binnen Euro Caps, specifiek de reeds vastgestelde keuze voor de Six Sigma methodiek met het DMAIC-raamwerk. Er wordt ingegaan op de noodzaak om, naast procesoptimalisatie, ook de organisatiestructuur en -cultuur te analyseren en waar nodig aan te passen om de effectiviteit van Six Sigma te maximaliseren en duurzame verbetering te garanderen. Tevens wordt de toegepaste onderzoeksmethode en de leeswijzer van het rapport uiteengezet.

Euro Caps staat voor belangrijke operationele en organisatorische uitdagingen die vragen om een gestructureerde veranderingsaanpak. Dit rapport biedt een analyse van de huidige situatie, een onderbouwd actieplan en een visie op de gewenste toekomstige staat. De aanbevelingen combineren bewezen methodieken uit veranderingsmanagement en procesoptimalisatie, afgestemd op de specifieke context van Euro Caps. In de vorige onderwijsperiode is een voorstel gedaan tot veranderingen, waarbij interne processen van Euro Caps onder de loep zijn genomen en mogelijkheden tot automatisering zijn onderzocht. Hierbij zijn knelpunten herkend en is de Six Sigma methodiek gekozen als passende kwaliteitsmanagementmethode om deze knelpunten te verhelpen, specifiek gericht op het optimaliseren van het vulproces van koffiecapsules en het minimaliseren van defecten volgens het DMAIC-raamwerk. Dit rapport bouwt voort op dit advies, door te bepalen wat er nodig is om de organisatie en haar medewerkers mee te krijgen in deze transitie.

De centrale vraag die in dit rapport wordt beantwoord, luidt: "Hoe kan Euro Caps op een effectieve en duurzame manier de organisatie inrichten en de cultuur ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma?"

Het doel van dit rapport is om een onderbouwd veranderplan te formuleren en een praktisch uitvoerbaar plan op te stellen waarmee Euro Caps de organisatorische transitie succesvol kan realiseren. Hierbij worden zowel interne als externe factoren in kaart gebracht en vertaald naar een concreet actie-, communicatie- en implementatieplan.

Om tot een volledig en bruikbaar advies te komen, worden de volgende deelvragen behandeld:
1. Welke veranderstrategie sluit het beste aan bij de organisatiekenmerken van Euro Caps om de Six Sigma implementatie te optimaliseren en duurzaam te verankeren?
2. Op welke wijze kan draagvlak worden gecreëerd bij interne en externe stakeholders voor de structurele en culturele veranderingen die nodig zijn ter ondersteuning van continue kwaliteitsverbetering?"""
    
    doc.add_paragraph(intro_text)
    
    # Add subsections
    doc.add_heading('1.1 Deskresearch methode', 2)
    method_text = """De methode van onderzoek bestaat primair uit deskresearch, waarbij gebruik is gemaakt van relevante literatuur en theorieën op het gebied van organisatiestructuur, organisatiecultuur, verandermanagement, communicatie en stakeholdermanagement. Specifiek zijn theorieën van Mintzberg over coördinatiemechanismen (Mintzberg, 1983) en Hofstede's cultuurdimensies voor organisaties (Hofstede, Hofstede & Minkov, 2010) toegepast om de huidige en gewenste situatie te analyseren. Voor de veranderstrategie is geput uit de BDK-theorie van Boonstra (Boonstra, 2018) en het achtstappenmodel van Kotter (Kotter, 1996), aangevuld met communicatieprincipes gebaseerd op de mensbeelden van De Caluwé (De Caluwé & Vermaak, 2009). De theorie van Kübler-Ross over de fasen van rouw (Kübler-Ross, 1969) is ingezet voor de analyse van mogelijke weerstanden. De informatie uit het Six Sigma projectdossier van Euro Caps is geïntegreerd om een realistische en praktijkgerichte benadering te waarborgen."""
    doc.add_paragraph(method_text)
    
    doc.add_heading('1.2 Leeswijzer', 2)
    reader_guide = """Dit adviesrapport is als volgt gestructureerd: Hoofdstuk 2 presenteert het theoretisch kader dat de basis vormt voor de analyse. Hoofdstuk 3 beschrijft de huidige situatie van Euro Caps, met aandacht voor de organisatiestructuur en -cultuur. Hoofdstuk 4 schetst de gewenste situatie, eveneens op het vlak van structuur en cultuur. Hoofdstuk 5 formuleert de veranderstrategie en het implementatieplan, inclusief een stakeholdersanalyse en een inschatting van mogelijke weerstanden. Hoofdstuk 6 detailleert het communicatieplan. Ten slotte biedt Hoofdstuk 7 de conclusie en concrete aanbevelingen voor Euro Caps."""
    doc.add_paragraph(reader_guide)
    
    return doc

def add_chapter_2(doc):
    """Add Chapter 2: Theoretical Framework with all visual indicators"""
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 2: Theoretisch kader', 1)
    
    intro_text = """Dit hoofdstuk is van fundamenteel belang voor het adviesrapport, aangezien het de theoretische grondslagen legt voor alle verdere analyses en aanbevelingen. Hierin worden de cruciale concepten en modellen geïntroduceerd en kort toegelicht, die onmisbaar zijn voor een diepgaand begrip van organisatiestructuren, -culturen en verandermanagementprocessen. Deze theoretische kaders zullen als leidraad dienen voor de beschrijving van zowel de huidige als de gewenste situatie, de selectie van de veranderstrategie en de ontwikkeling van het communicatieplan."""
    doc.add_paragraph(intro_text)
    
    # 2.1 Boonstra
    doc.add_heading('2.1 Veranderstrategieën volgens Boonstra', 2)
    boonstra_text = """Boonstra (2018) onderscheidt verschillende strategieën voor organisatieverandering, waarbij de keuze afhangt van factoren zoals urgentie, organisatiecultuur en gewenste betrokkenheid van medewerkers. De ontwikkelingsstrategie richt zich op verandering door collectief leren en participatie, wat tijdrovend maar duurzaam is. Een ingrijpende strategie kenmerkt zich door een top-down aanpak en snelheid, vooral geschikt bij urgente situaties, maar met risico op weerstand. De machtsstrategie legt de nadruk op controle en hiërarchie en is effectief in sterk taakgerichte contexten. Een onderhandelingsstrategie tracht belangen samen te brengen via overleg en vraagt tijd, maar creëert daardoor breed draagvlak. Tot slot stimuleert de verleidingsstrategie motivatie via voorbeeldgedrag en communicatie, wat goed werkt in mensgerichte culturen. Deze differentiatie biedt een kader voor het kiezen van de meest geschikte aanpak voor Euro Caps.

[Visual van Boonstra's veranderstrategieën hier. Bijvoorbeeld een matrix of schema dat de verschillende strategieën met hun kenmerken en toepassingsgebieden overzichtelijk weergeeft.]"""
    doc.add_paragraph(boonstra_text)
    
    # 2.2 De Caluwé
    doc.add_heading('2.2 Veranderkleuren van De Caluwé', 2)
    caluwe_text = """Het kleurenmodel van De Caluwé (De Caluwé & Vermaak, 2009) biedt een methodiek om verschillende 'denklogica's' over veranderen te visualiseren en te begrijpen. Blauwdrukdenken (Blauw) staat voor planmatige en rationele veranderingen, geschikt bij voorspelbare trajecten waarbij de uitkomst vooraf vaststaat. Geeldrukdenken (Geel) draait om macht, invloed en belangen, en is passend bij politieke omgevingen waar onderhandeling en coalitievorming essentieel zijn. Rooddrukdenken (Rood) focust op het motiveren van mensen door te bouwen aan relaties en samenwerking. Groendrukdenken (Groen) kenmerkt zich door leren en ontwikkelen, waarbij verandering ontstaat uit experiment en reflectie. Witdrukken (Wit) omarmt emergentie en de complexiteit van verandering die niet volledig te plannen is, en ziet verandering als een continu proces van co-creatie. Dit model helpt bij het afstemmen van communicatiestrategieën op de specifieke mindset van diverse stakeholdergroepen.

[Visual van De Caluwé's kleurenmodel hier. Bijvoorbeeld een radardiagram of een schema dat de vijf kleuren en hun bijbehorende denklogica's en mensbeelden weergeeft.]"""
    doc.add_paragraph(caluwe_text)
    
    return doc

def add_chapter_2_continued(doc):
    """Continue Chapter 2 with remaining sections"""

    # 2.3 Gap-analyse & Hofstede
    doc.add_heading('2.3 Gap-analyse & Hofstede-model', 2)
    gap_hofstede_text = """Een Gap-analyse is een essentiële tool voor verandermanagement die het verschil tussen de huidige situatie en de gewenste situatie in kaart brengt. Door deze 'kloof' te identificeren, kunnen specifieke veranderacties worden gepland om de gewenste staat te bereiken.

[Visual van een Gap-analyse model hier. Bijvoorbeeld een simpel schema met 'IST' aan de ene kant, 'SOLL' aan de andere, en de 'Gap' daartussenin, met pijlen die de acties aanduiden.]

Voor de analyse van de organisatiecultuur wordt gebruik gemaakt van Hofstede's cultuurdimensies (Hofstede, Hofstede & Minkov, 2010), die zes fundamentele dimensies van organisatiecultuur onderscheiden en zo helpen bij het typeren van de normen, waarden en gedragingen binnen een organisatie. Deze dimensies zijn:
1. Machtsafstand: De mate waarin minder machtige leden van organisaties accepteren dat macht ongelijk verdeeld is.
2. Individualisme versus Collectivisme: Geeft aan of de nadruk ligt op individuele prestatie en onafhankelijkheid, of op groepsloyaliteit en onderlinge verbondenheid.
3. Masculien versus Feminien: Een cultuur met een mascuiliene oriëntatie hecht waarde aan assertiviteit, competitie en materiële successen, terwijl een feminiene cultuur gericht is op samenwerking, bescheidenheid en de kwaliteit van het leven.
4. Lange- versus kortetermijngerichtheid: Deze dimensie beschrijft de focus op tradities en het verleden versus de focus op de toekomst, aanpassing en doorzettingsvermogen.
5. Toegeeflijkheid versus Terughoudendheid: De mate waarin de samenleving het bevredigen van menselijke basisbehoeften en genot toestaat versus de controle hierop door strikte sociale normen.

[Visual van Hofstede's cultuurdimensies hier. Bijvoorbeeld een illustratie van de zes dimensies met korte uitleg, of een spinnenwebdiagram met voorbeeldscores.]"""
    doc.add_paragraph(gap_hofstede_text)

    # 2.4 Kotter
    doc.add_heading('2.4 Kotter\'s 8 Stappenmodel', 2)
    kotter_text = """Kotter (1996) heeft een gestructureerd achtstappenmodel ontwikkeld dat dient als leidraad voor succesvolle organisatieverandering. Dit model begint met het creëren van urgentiebesef en het vormen van een leidende coalitie, stappen die essentieel zijn voor het opbouwen van momentum. De volgende stappen omvatten het ontwikkelen van een visie en strategie, en het effectief communiceren van deze visie om draagvlak te creëren. Verdere stappen richten zich op het wegnemen van obstakels, het genereren van korte-termijn successen, het consolideren van verbeteringen voor duurzame verandering, en het uiteindelijk verankeren van de nieuwe benaderingen in de organisatiecultuur. Dit sequentiële model biedt een praktische aanpak voor complexe verandertrajecten.

[Visual van Kotter's 8-stappenmodel hier. Bijvoorbeeld een infographic die de 8 stappen in volgorde weergeeft, eventueel met korte omschrijving per stap.]"""
    doc.add_paragraph(kotter_text)

    # 2.5 Stakeholderanalyse
    doc.add_heading('2.5 Stakeholderanalyse', 2)
    stakeholder_text = """Een stakeholderanalyse is een belangrijke tool in verandermanagement om de invloed en belangen van alle betrokken partijen bij een verandering te analyseren (Boonstra, 2018). Het proces begint met het identificeren van alle stakeholders, zowel intern als extern. Vervolgens vindt een categorisatie van deze stakeholders plaats op basis van hun macht (invloed) en belang ten opzichte van het project, om zo prioriteit en een passende communicatie- en betrokkenheidsstrategie te bepalen. Dit inzicht is cruciaal voor het managen van verwachtingen en het mitigeren van weerstand.

[Visual van een algemeen Stakeholderanalyse model hier. Bijvoorbeeld een Power/Interest matrix, of een diagram met stappen voor het uitvoeren van een stakeholderanalyse.]"""
    doc.add_paragraph(stakeholder_text)

    # 2.6 Kübler-Ross
    doc.add_heading('2.6 Verandercurve van Kübler-Ross', 2)
    kubler_ross_text = """De verandercurve van Kübler-Ross (1969), oorspronkelijk ontwikkeld om de emotionele fasen van rouw te beschrijven, wordt in verandermanagement gebruikt om de typische emotionele fases te duiden die mensen doorlopen wanneer zij geconfronteerd worden met ingrijpende veranderingen. Deze fases omvatten ontkenning, waarbij de verandering wordt afgewezen; frustratie, die kan leiden tot weerstand en woede; onderhandeling, een poging om de verandering af te zwakken of te beïnvloeden; depressie, een gevoel van onmacht of verdriet over het verlies van het oude; en uiteindelijk acceptatie, waarbij de nieuwe situatie wordt omarmd. Het begrijpen van deze curve helpt leidinggevenden om menselijke reacties op verandering te anticiperen en effectief te begeleiden.

[Visual van de Verandercurve van Kübler-Ross hier. Bijvoorbeeld een grafiek die de verschillende emotionele fasen op een tijdas weergeeft en de emotionele staat van mensen tijdens verandering illustreert.]"""
    doc.add_paragraph(kubler_ross_text)

    transition_text = """Met de theoretische kaders op hun plaats, kan nu de huidige staat van Euro Caps gedetailleerd worden onderzocht."""
    doc.add_paragraph(transition_text)

    return doc

def add_chapter_3(doc):
    """Add Chapter 3: Current Situation"""
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 3: Huidige situatie', 1)

    intro_text = """Dit hoofdstuk is gewijd aan een diepgaande analyse van de huidige organisatorische context van Euro Caps. Het biedt een helder beeld van hoe de organisatie momenteel functioneert, zowel op het vlak van structuur als cultuur. Door de toepassing van de eerder geïntroduceerde theoretische kaders, worden de dominante kenmerken en eventuele knelpunten van de huidige situatie blootgelegd, wat een cruciale basis vormt voor het formuleren van effectieve veranderingsstrategieën."""
    doc.add_paragraph(intro_text)

    # 3.1 Current organizational structure
    doc.add_heading('3.1 Huidige organisatiestructuur', 2)
    current_structure_text = """De organisatiestructuur van Euro Caps wordt gekenmerkt door functionele silo's (Operatie, MCO, Sales, Finance, IT) met beperkte horizontale communicatie. De toepassing van coördinatiemechanismen van Mintzberg (1983) toont een beperkte onderlinge aanpassing door deze silo-vorming. Direct toezicht is sterk aanwezig, met name in de productieprocessen. Standaardisatie van werkprocessen ontbreekt in veel processen, terwijl standaardisatie van output voornamelijk voor eindproducten geldt. Standaardisatie van vaardigheden is beperkt, vooral tussen afdelingen.

Voor Euro Caps is het van belang een organisatiestructuur te kiezen die aansluit bij haar ambities en de behoeften van haar operaties. Een beslissingsmatrix op basis van het Mintzberg-model (Mintzberg, 1983) vergelijkt verschillende typen organisaties op basis van hun kenmerken en relevantie voor Euro Caps. Deze matrix, weergegeven in Tabel 1, evalueert diverse Mintzberg-configuraties op factoren zoals standaardisatie en controle, innovatievermogen, flexibiliteit en klantgerichtheid, technologische innovatie, productefficiëntie, en een gecombineerde score voor klantgerichtheid en efficiëntie, allemaal beoordeeld op een schaal van 1 (laag) tot 5 (hoog)."""
    doc.add_paragraph(current_structure_text)

    return doc

def add_chapter_3_continued(doc):
    """Continue Chapter 3 with decision matrix and organizational culture"""

    # Add decision matrix table
    table_text = """
Tabel 1: Beslissingsmatrix organisatiestructuur

| Organisatiestructuur      | Standaardisatie & controle | Innovatievermogen | Flexibiliteit & klantgerichtheid | Technologische innovatie | Productefficiëntie | Klantgerichtheid & efficiëntie | Score |
| :------------------------ | :------------------------- | :---------------- | :------------------------------- | :----------------------- | :------------------ | :----------------------------- | :---- |
| Ondernemende organisatie  | 2                          | 5                 | 4                                | 3                        | 2                   | 3                              | 19    |
| Machinebureaucratie       | 5                          | 1                 | 1                                | 3                        | 5                   | 4                              | 19    |
| Professionele organisatie | 4                          | 2                 | 2                                | 2                        | 4                   | 3                              | 17    |
| Gediversifieerde organisatie | 3                          | 2                 | 3                                | 3                        | 3                   | 3                              | 17    |
| Innovatieve organisatie   | 1                          | 5                 | 5                                | 4                        | 1                   | 2                              | 18    |
| Missionaire organisatie   | 2                          | 2                 | 3                                | 2                        | 2                   | 3                              | 14    |
| Politieke organisatie     | 1                          | 1                 | 1                                | 2                        | 1                   | 2                              | 8     |
"""
    doc.add_paragraph(table_text)

    analysis_text = """Uit deze beslissingsmatrix blijkt dat de Machinebureaucratie en de Ondernemende organisatie beide een score van 19 behalen, gevolgd door de Innovatieve organisatie met een score van 18. Dit bevestigt de eerdere waarneming dat Euro Caps kenmerken vertoont van zowel een machineorganisatie als een innovatieve organisatie. De machinebureaucratie is duidelijk zichtbaar in de gestandaardiseerde productieprocessen en de nadruk op efficiëntie in de massaproductie, waarbij dagelijks ongeveer 3 miljoen capsules worden geproduceerd. De dominante coördinatiemechanismen van Mintzberg (1983) zijn hier directe supervisie in de productieomgeving, standaardisatie van werkprocessen om consistente kwaliteit te waarborgen, en standaardisatie van resultaten met duidelijke doelstellingen zoals dagelijkse productieaantallen en kwaliteitsnormen. Tegelijkertijd vertoont het bedrijf kenmerken van een innovatieve organisatie door de nadruk op innovatie en ontwikkeling van nieuwe producten, wat duidt op de aanwezigheid van wederzijdse aanpassing tussen medewerkers bij complexe problemen en productontwikkeling. De vijf basisonderdelen van een organisatie, zoals gedefinieerd door Mintzberg, zijn aanwezig: de strategische top (directie, topmanagement zoals CEO Nils Clement), het middenkader (afdelingshoofden, teamleiders zoals productieleiders Maik Ritter en Maria Stanić), de operationele kern (productiemedewerkers zoals Ismail Berenschot en Samantha Mukhlis Aswad), de technische staf (kwaliteitscontrole zoals Hoofd Kwaliteitsbeheer Kees Keurig, productieplanning en technische ondersteuning) en de ondersteunende staf (HR met Uwe Regel, Financiën met Berkan Arrindell, Logistiek met Rijk Wegen, Ko Jager, en Medewerker Logistiek Tila Karren). Standaardisatie van kennis en vaardigheden wordt gestimuleerd door interne opleidingen en trainingsprogramma's, essentieel voor een organisatie die streeft naar precisie en continue verbetering. De rol van Servé Bosland als Manager Bedrijfsvoering en Erik Dekker als Manager ICT zal waarschijnlijk een hybride karakter hebben, balancerend tussen operationele standaardisatie en het stimuleren van innovatie.

[Visual van Beslissingsmatrix Mintzberg hier. Bijvoorbeeld een spindiagram of staafdiagram van scores per organisatiestructuur, of een markering in de tabel die de gekozen structuur benadrukt. Dit zou een PNG moeten zijn die de matrix visueel ondersteunt.]"""
    doc.add_paragraph(analysis_text)

    # 3.2 Current organizational culture
    doc.add_heading('3.2 Huidige organisatiecultuur', 2)
    culture_text = """Wat betreft de huidige organisatiecultuur van Euro Caps, kan deze worden geanalyseerd aan de hand van Hofstede's cultuurdimensies (Hofstede, Hofstede & Minkov, 2010). Euro Caps kenmerkt zich door een relatief kleine machtsafstand, wat blijkt uit de toegankelijkheid van het management en de open communicatie tussen verschillende organisatieniveaus, zoals door de organisatie zelf gecommuniceerd. Er is een balans tussen individualisme en collectivisme, waarbij individueel initiatief wordt aangemoedigd, maar teamwerk en samenwerking centraal staan. De cultuur neigt naar femininiteit met een focus op goede werkrelaties, samenwerking en zorg voor medewerkers via opleidingen en persoonlijke ontwikkeling. De onzekerheidsvermijding is gemiddeld tot hoog door gestandaardiseerde werkprocessen, maar de organisatie biedt wel ruimte voor innovatie. De organisatie heeft een lange termijn oriëntatie, gericht op duurzame groei en ontwikkeling, zoals blijkt uit investeringen in nieuwe productiefaciliteiten en de ambitie om in tien jaar tijd tien miljard capsules te produceren. Tot slot toont Euro Caps een cultuur van toegeeflijkheid met ruimte voor plezier naast hard werken. De kernwaarden van het bedrijf, 'Ondernemend' ("Kan niet bestaat niet"), 'Partnerschap', 'Eigenzinnig' (ruimte voor ideeën en feedback) en 'Nu' (focus op heden, leren van verleden, kijken naar toekomst), bevestigen de beschreven culturele kenmerken en tonen aan dat de organisatie al een basis heeft voor verandering en aanpassing. Deze combinatie van een machineachtige structuur met innovatieve elementen, en een cultuur die openstaat voor ontwikkeling, maakt Euro Caps uniek.

[Visual van Hofstede dimensies voor Euro Caps hier. Bijvoorbeeld een radardiagram van de scores op elke dimensie die de cultuur van Euro Caps in één oogopslag weergeeft, of een staafdiagram voor elke dimensie. Dit zou een PNG moeten zijn die de analyse visueel ondersteunt.]"""
    doc.add_paragraph(culture_text)

    # 3.3 Partial conclusion
    doc.add_heading('3.3 Deelconclusie beantwoorden', 2)
    conclusion_text = """De huidige situatie van Euro Caps kenmerkt zich door een functionele organisatiestructuur die een combinatie is van een machineorganisatie met sterke standaardisatie van werkprocessen en output, en elementen van een innovatieve organisatie met wederzijdse aanpassing (Mintzberg, 1983). De cultuur is een mix van resultaatgerichtheid en mensgerichtheid, met een relatief lage machtsafstand, neigend naar collectivisme en masculiniteit, en een hoge onzekerheidsvermijding met kortetermijnfocus (Hofstede, Hofstede & Minkov, 2010). Deze eigenschappen, samen met de geïdentificeerde knelpunten in productieprocessen, communicatie en IT-systemen, wijzen op een sterke behoefte aan verandering om de Six Sigma implementatie te optimaliseren en duurzame verbetering te realiseren. De stakeholderanalyse identificeert sleutelfiguren, potentiële blockers en floaters die strategisch moeten worden betrokken bij de verandering.

Na een helder beeld van de huidige situatie is het cruciaal om te bepalen waar Euro Caps naartoe wil bewegen."""
    doc.add_paragraph(conclusion_text)

    return doc

def add_chapter_4(doc):
    """Add Chapter 4: Desired Situation"""
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 4: Gewenste situatie', 1)

    intro_text = """In dit hoofdstuk wordt het toekomstbeeld voor Euro Caps geschetst, waarbij de ideale staat van zowel de organisatiestructuur als de organisatiecultuur wordt gedefinieerd. Voortbouwend op de gedetailleerde analyse van de huidige situatie, wordt hier beschreven hoe Euro Caps zich kan ontwikkelen om niet alleen de effectiviteit van de Six Sigma methodiek te maximaliseren, maar ook om als organisatie adaptiever, innovatiever en duurzaam succesvol te zijn. Dit omvat een optimale balans tussen efficiëntie en flexibiliteit, en tussen procesfocus en een versterkte mensgerichtheid."""
    doc.add_paragraph(intro_text)

    # 4.1 Desired organizational structure
    doc.add_heading('4.1 Gewenste organisatiestructuur', 2)
    desired_structure_text = """De gewenste situatie voor Euro Caps bouwt voort op de sterke punten van de huidige organisatie, met een versterking van flexibiliteit, innovatie en adaptief vermogen, essentieel voor een voortdurende verbetering en een duurzame concurrentiepositie. De Six Sigma methodiek, hoewel uitstekend voor procesoptimalisatie, vereist een organisatie die openstaat voor continue verbetering en veranderingen effectief kan implementeren en borgen. De integratie van HACCP-elementen in Six Sigma, zoals eerder gedefinieerd in de basisdocumentatie, benadrukt de noodzaak van een hybride aanpak die zowel kwaliteit als voedselveiligheid optimaliseert.

De gewenste organisatiestructuur voor Euro Caps zal nog steeds een solide basis hebben in de standaardisatie van werkprocessen en output, aangezien precisie en kwaliteit essentieel blijven in de voedingsmiddelenindustrie (Mintzberg, 1983). Echter, er dient meer nadruk te komen op wederzijdse aanpassing, met name op de hogere managementniveaus en bij multidisciplinaire projectteams die betrokken zijn bij Six Sigma-projecten. Dit betekent dat er meer ruimte komt voor overleg en informele communicatie tussen afdelingen om problemen gezamenlijk op te lossen en processen te optimaliseren. De rollen van managers zoals Servé Bosland (Manager Bedrijfsvoering), Erik Dekker (Manager ICT), Rijk Wegen (Manager Logistiek) en Ko Jager (Manager Inkoop) verschuiven meer naar het faciliteren van cross-functionele teams en het wegnemen van barrières. Productiemanagers Maik Ritter en Maria Stanić worden coachende leiders die medewerkers als Ismail Berenschot en Samantha Mukhlis Aswad in staat stellen om zelfstandig verbeteringen te initiëren. Daarnaast kan overwogen worden om de standaardisatie van vaardigheden te versterken door middel van gerichte trainingen en kennisdeling, waardoor medewerkers beter in staat zijn om zelfstandig problemen te identificeren en op te lossen, en zo de uitvoerende processen flexibeler te maken. Dit draagt bij aan de empowerment van medewerkers en vergroot hun betrokkenheid bij de kwaliteitsverbetering."""
    doc.add_paragraph(desired_structure_text)

    return doc

def add_chapter_4_continued(doc):
    """Continue Chapter 4 with organizational culture and conclusion"""

    # 4.2 Desired organizational culture
    doc.add_heading('4.2 Gewenste organisatiecultuur', 2)
    desired_culture_text = """De organisatiestructuur van Euro Caps is momenteel sterk gericht op structuur, controle en het behalen van resultaten. Door hoge productieniveaus en vaste werkinstructies ligt de nadruk op taakgericht werken, standaardisatie en prestatiebewaking via KPI's. Met de verdere integratie van de Six Sigma methodiek, met de nadruk op datagestuurde procesoptimalisatie en continue verbetering, zijn er duidelijke cultuurveranderingen nodig om deze systemen succesvol te implementeren en duurzaam te verankeren in de organisatie.

Een belangrijke verschuiving is de toename van zelfstandigheid bij medewerkers. Voorheen werkten zij mogelijk meer op basis van directe instructies van hun leidinggevende. In de nieuwe situatie, aangedreven door de data en analyses uit Six Sigma, zullen taken vaker gebaseerd zijn op systematische inzichten. Medewerkers moeten hierdoor in toenemende mate zelfstandig kunnen werken, snel kunnen schakelen bij afwijkingen en een dieper inzicht hebben in hun verantwoordelijkheden binnen het geoptimaliseerde proces. Dit vereist een fundamentele verschuiving van de rol van de leidinggevende: van aansturing en directe supervisie naar begeleiding, coaching en facilitering van zelfsturende teams. Daarbij is het belangrijk dat leidinggevenden, zoals de productiemanagers Maik Ritter en Maria Stanić, ook het waarom achter de verandering en de voordelen van de Six Sigma aanpak duidelijk toelichten, zodat medewerkers de bredere context van hun bijdrage begrijpen.

Daarnaast verandert de samenwerking tussen afdelingen. Doordat de Six Sigma methodiek processen op basis van realtimedata optimaliseert, ontstaat er minder ruimte voor handmatige en ad-hoc afstemming die voorheen wellicht gebruikelijk was. Dit betekent dat er duidelijkere afspraken moeten komen over taken en processen, en dat procedures moeten worden opgesteld voor uitzonderingen of systeemfouten die buiten de gestandaardiseerde Six Sigma-flow vallen. Dit vraagt om een cultuur waarin open communicatie, het nakomen van afspraken en een collectief gevoel van eigenaarschap centraal staan. De culturele dimensies zoals mensgerichtheid en openheid (Hofstede, 2010) zullen verder worden versterkt om een dergelijke collaboratieve en lerende omgeving te bevorderen, essentieel voor een succesvolle en duurzame Six Sigma implementatie."""
    doc.add_paragraph(desired_culture_text)

    # 4.3 Partial conclusion
    doc.add_heading('4.3 Deelconclusie beantwoorden', 2)
    conclusion_text = """De gewenste situatie voor Euro Caps kenmerkt zich door een robuuste organisatiestructuur die een balans vindt tussen standaardisatie en flexibiliteit door middel van versterkte wederzijdse aanpassing en standaardisatie van vaardigheden (Mintzberg, 1983). De organisatiecultuur zal naast de bestaande resultaat- en procesgerichtheid, een grotere nadruk leggen op mensgerichtheid, leren, en openheid (Hofstede, Hofstede & Minkov, 2010), waardoor een cultuur van continue verbetering en innovatie verder wordt gestimuleerd en geborgd. Specifiek vereisen de Six Sigma implementaties een verhoogde zelfstandigheid van medewerkers, een verschuiving in leiderschapsstijl naar coaching, en een cultuur van duidelijke afspraken en eigenaarschap in interafdelingssamenwerking. Deze gewenste situatie creëert de ideale voedingsbodem voor het duurzame succes van de Six Sigma implementatie en de ambitie om koploper te blijven in kwaliteit en efficiëntie.

Met een duidelijk beeld van de gewenste situatie, is het nu tijd om de concrete stappen te definiëren om deze te bereiken."""
    doc.add_paragraph(conclusion_text)

    return doc

def add_chapter_5_part1(doc):
    """Add Chapter 5 Part 1: Change Strategy and Implementation Plan - Preparatory Phase"""
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 5: Veranderstrategie + implementatieplan', 1)

    intro_text = """Dit hoofdstuk vormt de kern van het adviesrapport, aangezien hier de specifieke veranderstrategie voor Euro Caps wordt uiteengezet en een concreet implementatieplan wordt gepresenteerd. Het begint met een gedetailleerd voorbereidende fase, die de noodzakelijke structurele en culturele veranderingen identificeert en de betrokken stakeholders analyseert op mogelijke weerstanden. Vervolgens wordt dieper ingegaan op de onderbouwing van de gekozen veranderstrategie en de implementatie ervan, inclusief de cruciale rol van Six Sigma in dit gehele transformatieproces."""
    doc.add_paragraph(intro_text)

    # 5.1 Preparatory phase
    doc.add_heading('5.1 Voorbereidende deel', 2)
    prep_intro = """De voorbereiding op de verandering omvat een zorgvuldige analyse van de te verwachten aanpassingen in zowel structuur als cultuur, gevolgd door een diepgaande stakeholdersanalyse en een inschatting van mogelijke weerstanden."""
    doc.add_paragraph(prep_intro)

    # 5.1.1 Organizational structure changes
    doc.add_heading('5.1.1 Organisatiestructuur veranderingen', 3)
    structure_changes_text = """De structurele veranderingen richten zich op het faciliteren van meer wederzijdse aanpassing en standaardisatie van vaardigheden binnen Euro Caps, ter ondersteuning van de Six Sigma aanpak (Mintzberg, 1983). Dit betekent het implementeren van meer multidisciplinaire teams, vooral rondom specifieke Six Sigma projecten (DMAIC-cycli), waarin medewerkers van verschillende afdelingen met diverse expertise samenwerken aan procesverbeteringen. De oprichting van 'werkgroepen kwaliteit' of 'verbeterkringen' kan de dialoog en probleemoplossing bevorderen. De rol van het middenkader, waaronder productiemanagers Maik Ritter en Maria Stanić, en managers als Servé Bosland, Erik Dekker, Rijk Wegen en Ko Jager, zal verschuiven van puur controlerend naar meer coachend en faciliterend, waarbij zij worden getraind in het ondersteunen van teamwerk en het identificeren van kansen voor procesoptimalisatie. De communicatielijnen worden verkort en informeler gemaakt, met regelmatige overlegstructuren om snelle besluitvorming en aanpassingen te bevorderen. Formele procedures voor kennisdeling, zoals een intern kennisplatform, worden geïntroduceerd om de standaardisatie van vaardigheden te ondersteunen en te zorgen dat geleerde lessen uit Six Sigma projecten breed worden gedeeld."""
    doc.add_paragraph(structure_changes_text)

    # 5.1.2 Organizational culture changes
    doc.add_heading('5.1.2 Organisatiecultuur veranderingen', 3)
    culture_changes_text = """De culturele veranderingen zijn gericht op het cultiveren van een mensgerichte en lerende organisatiecultuur, naast de bestaande focus op kwaliteit en resultaten (Hofstede, Hofstede & Minkov, 2010). Dit vereist het proactief stimuleren van feedback en dialoog, zowel top-down als bottom-up. Trainingen in communicatievaardigheden en conflictbemiddeling voor leidinggevenden zijn essentieel om een open en veilige omgeving te creëren waarin medewerkers zich vrij voelen om ideeën en zorgen te uiten. Het erkennen en belonen van initiatieven voor verbetering, ongeacht de uitkomst, draagt bij aan een experimenteer- en leercultuur. Succesverhalen van Six Sigma projecten, inclusief de integratie van HACCP, worden breed gecommuniceerd om het belang van continue verbetering te onderstrepen en medewerkers te inspireren. Leiderschap speelt een cruciale rol in het voorleven van de gewenste cultuur door transparantie, openheid en een focus op ontwikkeling. Workshops over het belang van individuele bijdragen aan de organisatiekwaliteit en het grotere geheel zullen medewerkers, zoals Ismail Berenschot en Samantha Mukhlis Aswad, motiveren en hun betrokkenheid vergroten. De integratie van duurzaamheid in de missie en visie kan hierbij als concrete culturele verandering worden ingebed, bijvoorbeeld door een jaarlijkse "Green Innovation Day". HR Manager Uwe Regel speelt een sleutelrol in het verankeren van deze culturele verschuivingen in beleid en praktijk."""
    doc.add_paragraph(culture_changes_text)

    return doc

def add_stakeholder_analysis(doc):
    """Add detailed stakeholder analysis with tables"""

    # 5.1.3 Stakeholder analysis
    doc.add_heading('5.1.3 Stakeholdersanalyse', 3)
    stakeholder_intro = """Voor een succesvolle implementatie is het essentieel om een helder beeld te hebben van de betrokken stakeholders. De onderstaande tabellen bieden een overzicht van de belangrijkste stakeholders, hun rol, mate van betrokkenheid, belang, positie en invloed, alsook een categorisatie voor een goede uitvoering van het implementatieadvies."""
    doc.add_paragraph(stakeholder_intro)

    # Table 2: Stakeholder overview
    doc.add_paragraph("Tabel 2: Overzicht stakeholders")
    stakeholder_table = """
| Stakeholdergroep | Naam stakeholder        | Functie                              | Betrokkenheid | Belang    | Positie | Invloed |
| :--------------- | :---------------------- | :----------------------------------- | :------------ | :-------- | :------ | :------ |
| ICT              | Erik Dekker             | Manager ICT                          | Hoog          | Hoog      | Mover   | Hoog    |
| Productie        | Maik Ritter             | Productiemanager                     | Gemiddeld     | Hoog      | Blocker | Hoog    |
| CEO              | Nils Clement            | CEO                                  | Hoog          | Hoog      | Mover   | Hoog    |
| Logistiek        | Rijk Wegen              | Manager Logistiek                    | Gemiddeld     | Hoog      | Floater | Hoog    |
| Productie        | Ismail Berenschot       | Productiemedewerker                  | Gemiddeld     | Gemiddeld | Blocker | Laag    |
| Logistiek        | Tila Karren             | Medewerker Logistiek (Inkomend/Uitgaand) | Gemiddeld     | Gemiddeld | Floater | Gemiddeld |
| Inkoop           | Ko Jager                | Manager Inkoop                       | Gemiddeld     | Gemiddeld | Floater | Gemiddeld |
| Bedrijfsvoering  | Servé Bosland           | Manager Bedrijfsvoering              | Hoog          | Hoog      | Mover   | Hoog    |
| HR               | Uwe Regel               | HR Manager                           | Gemiddeld     | Gemiddeld | Floater | Laag    |
| Productie        | Maria Stanić            | Productiemanager                     | Gemiddeld     | Hoog      | Blocker | Hoog    |
| Kwaliteitsbeheer | Kees Keurig             | Hoofd Kwaliteitsbeheer               | Gemiddeld     | Gemiddeld | Floater | Gemiddeld |
| Projectleiding   | Niene Tepe              | Projectleider                        | Hoog          | Hoog      | Mover   | Hoog    |
| Financiën        | Berkan Arrindell        | Hoofd Financiën                      | Hoog          | Hoog      | Mover   | Hoog    |
| Productie        | Samantha Mukhlis Aswad | Productiemedewerker                  | Gemiddeld     | Gemiddeld | Blocker | Laag    |
"""
    doc.add_paragraph(stakeholder_table)

    return doc

def add_stakeholder_categorization(doc):
    """Add stakeholder categorization table and resistance analysis"""

    # Table 3: Stakeholder categorization
    doc.add_paragraph("Tabel 3: Categorisatie van de stakeholders")
    categorization_table = """
| Soort stakeholder    | Primair (directe invloed)                                                 | Secundair (indirecte invloed)                                                                                                                                                                             |
| :------------------- | :------------------------------------------------------------------------ | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Interne stakeholders | - CEO (Nils Clement)<br>- Projectleider (Niene Tepe)<br>- Manager Bedrijfsvoering (Servé Bosland)<br>- Manager ICT (Erik Dekker)<br>- Hoofd Financiën (Berkan Arrindell) | - Productiemanagers (Maik Ritter, Maria Stanić)<br>- Manager Logistiek (Rijk Wegen)<br>- Manager Inkoop (Ko Jager)<br>- HR Manager (Uwe Regel)<br>- Hoofd Kwaliteitsbeheer (Kees Keurig)<br>- Productiemedewerkers (Ismail Berenschot, Samantha Mukhlis Aswad, Tila Karren)<br>- Ondernemingsraad (OR) |
| Externe stakeholders | - Leveranciers (specifieke Six Sigma partners, machines, etc.)          | - Overheid / NVWA (voedselveiligheid)<br>- Certificeringsinstanties (UTZ, Organic, Fair Trade)                                                                                           |
| Interface stakeholders | –                                                                         | - Klanten (retailers, koffiebranders)                                                                                                                                                                     |
"""
    doc.add_paragraph(categorization_table)

    explanation_text = """De positie-categorie (Mover, Blocker, Floater) geeft inzicht in hoe elke stakeholder zich opstelt ten opzichte van het verandertraject: een Mover is actief betrokken, denkt mee en stimuleert de verandering; een Blocker is kritisch of terughoudend en kan de verandering belemmeren; een Floater is neutraal en beweegt mee met de situatie. Deze classificatie helpt bij het bepalen van de juiste communicatie- en interventiestrategie.

[Visual van Stakeholdermatrix hier. Bijvoorbeeld een Power/Interest Matrix met de stakeholders gepositioneerd in de kwadranten, of een staafdiagram dat de verdeling van Movers/Blockers/Floaters toont.]"""
    doc.add_paragraph(explanation_text)

    # 5.1.4 Kübler-Ross resistance analysis
    doc.add_heading('5.1.4 Mogelijke weerstanden van Kübler-Ross', 3)
    resistance_text = """Mogelijke weerstanden kunnen zich bij de verschillende stakeholdergroepen manifesteren in diverse vormen, zoals beschreven in de fasen van rouw van Kübler-Ross (1969). Bij de Directie (Nils Clement, Servé Bosland, Berkan Arrindell) kan in eerste instantie ontkenning van de noodzaak tot diepgaande cultuurverandering optreden, vooral als de huidige Six Sigma resultaten al goed zijn en er geen externe druk wordt gevoeld voor verdere organisatorische aanpassingen. Dit kan zich uiten in gedrag van uitstel of het minimaliseren van de urgentie, mogelijk door de verandering als "nice-to-have" in plaats van "must-have" te beschouwen. Het Middenkader (Erik Dekker, Maik Ritter, Maria Stanić, Rijk Wegen, Ko Jager, Tila Karren, Kees Keurig, Uwe Regel) kan woede en frustratie ervaren over de verschuiving in hun rol en verantwoordelijkheden (van puur controlerend naar coachend), wat kan leiden tot weerstand in de vorm van passieve obstructie of openlijke kritiek, vooral bij managers die gewend zijn aan traditionele hiërarchieën en een verhoogde werkdruk ervaren door de verandering. Productiemedewerkers (Ismail Berenschot, Samantha Mukhlis Aswad) kunnen een gevoel van angst of zelfs depressie ervaren door de veranderingen in werkprocessen en de angst voor het onbekende, bijvoorbeeld de vrees voor nieuwe taken door Six Sigma-processen of de angst voor fouten bij nieuwe methoden. Dit kan zich uiten in verminderde motivatie, verhoogd ziekteverzuim, of vasthouden aan oude routines. Er kan ook sprake zijn van onderhandelen over de mate van verandering, waarbij geprobeerd wordt de impact te minimaliseren, bijvoorbeeld door te proberen kleine wijzigingen door te voeren in plaats van grotere, systemische veranderingen. Voor alle groepen geldt dat duidelijke en herhaalde communicatie essentieel is om door deze fasen heen te komen en uiteindelijk acceptatie te bevorderen.

[Visual van de Verandercurve van Kübler-Ross hier. Bijvoorbeeld een grafiek die de verschillende fasen op een tijdas weergeeft en de emotionele staat van medewerkers illustreert.]"""
    doc.add_paragraph(resistance_text)

    return doc

def add_chapter_5_part2(doc):
    """Add Chapter 5 Part 2: Implementation Strategy"""

    # 5.2 Implementation phase
    doc.add_heading('5.2 Uitvoerende deel', 2)
    implementation_intro = """Het uitvoerende deel richt zich op de gekozen veranderstrategie en het implementatieplan, met specifieke aandacht voor de rol van Six Sigma."""
    doc.add_paragraph(implementation_intro)

    # 5.2.1 Strategic change approach
    doc.add_heading('5.2.1 Strategische veranderaanpak', 3)
    strategic_approach_text = """Voor het bepalen van de juiste veranderaanpak bij Euro Caps is gekozen voor het model van Boonstra & Maasdaar (2008). Hierbij wordt gekeken naar de verhouding tussen organisatiecultuur, structuur en veranderbereidheid. De volgende beslissingsmatrix (Tabel 4) illustreert de afweging tussen verschillende veranderstrategieën op basis van hun geschiktheid voor de cultuur en structuur van Euro Caps, de verwachte weerstand, de potentie voor samenwerking en de langetermijneffecten. Een hogere score (schaal 1-5) duidt op een betere aansluiting bij de kenmerken van Euro Caps."""
    doc.add_paragraph(strategic_approach_text)

    # Table 4: Boonstra Decision Matrix
    doc.add_paragraph("Tabel 4: Beslissingsmatrix (Boonstra)")
    boonstra_table = """
| Strategie               | Past bij cultuur | Past bij structuur | Weinig weerstand | Sterke samenwerking | Langetermijn effect | Totale score |
| :---------------------- | :--------------- | :----------------- | :--------------- | :------------------ | :----------------- | :----------- |
| Ontwikkelingsstrategie  | 3                | 3                  | 4                | 5                   | 5                  | 20           |
| Interne strategie       | 2                | 3                  | 2                | 4                   | 4                  | 15           |
| Machtsstrategie         | 1                | 2                  | 3                | 3                   | 4                  | 13           |
| Onderhandelingsstrategie | 4                | 4                  | 4                | 4                   | 4                  | 20           |
"""
    doc.add_paragraph(boonstra_table)

    analysis_text = """Uit Tabel 4 blijkt dat zowel de Ontwikkelingsstrategie als de Onderhandelingsstrategie de hoogste score behalen (beide 20 punten). Gezien de aard van de Six Sigma implementatie, die sterk afhankelijk is van continue verbetering en actieve participatie van medewerkers, wordt de ontwikkelingsstrategie als meest passend beschouwd. Deze benadering sluit aan bij de bestaande innovatieve aspecten van Euro Caps en de gewenste mensgerichte cultuur. Hoewel de onderhandelingsstrategie ook hoog scoort op draagvlak en samenwerking, ligt de nadruk meer op belangenafstemming dan op het collectieve leerproces dat kenmerkend is voor Six Sigma en de gewenste cultuurverandering.

[Visual van Boonstra's Beslissingsmatrix hier. Bijvoorbeeld een staafdiagram van de totale scores per strategie, of een radardiagram die de prestaties op elke factor per strategie vergelijkt. Dit helpt de keuze visueel te onderbouwen.]"""
    doc.add_paragraph(analysis_text)

    return doc

def add_remaining_chapters(doc):
    """Add the remaining chapters to complete the document"""

    # 5.2.2 Veranderstrategie Boonstra
    doc.add_heading('5.2.2 Veranderstrategie Boonstra', 3)
    boonstra_strategy_text = """Op basis van de beslissingsmatrix wordt de ontwikkelingsstrategie van Boonstra gekozen als de meest geschikte aanpak voor Euro Caps. Deze strategie kenmerkt zich door een participatieve benadering waarbij medewerkers actief betrokken worden bij het veranderproces. De ontwikkelingsstrategie sluit aan bij de bestaande cultuur van Euro Caps die openstaat voor innovatie en verbetering, en past bij de aard van Six Sigma die sterk afhankelijk is van medewerkersbetrokkenheid en continue leren.

De implementatie van deze strategie houdt in dat veranderingen bottom-up worden geïnitieerd en ondersteund, waarbij medewerkers op alle niveaus worden aangemoedigd om verbetervoorstellen te doen en actief deel te nemen aan Six Sigma projecten. Dit creëert een gevoel van eigenaarschap en verhoogt de kans op succesvolle implementatie en duurzame verankering van de veranderingen."""
    doc.add_paragraph(boonstra_strategy_text)

    # 5.2.3 Veranderaanpak Kotter
    doc.add_heading('5.2.3 Veranderaanpak Kotter', 3)
    kotter_approach_text = """De implementatie van de Six Sigma aanpak bij Euro Caps zal gefaseerd plaatsvinden over een periode van 21 maanden, verdeeld in drie hoofdfasen, geïntegreerd met Kotter's acht stappen:

**Fase 1: Voorbereiding (3 maanden)**
Deze fase correspondeert met Kotter's stappen 1, 2, en 3. De focus ligt op het creëren van een stevige basis en een duidelijke routekaart voor de verandering.

1. **Creëer een gevoel van urgentie:** De noodzaak van continue kwaliteitsverbetering en efficiëntie om concurrentievoordeel te behouden, wordt door CEO Nils Clement en Manager Bedrijfsvoering Servé Bosland intern benadrukt. Dit gebeurt door concrete cijfers over huidige verspillingen en gemiste kansen te presenteren, mede gebaseerd op Six Sigma metingen uit de 'Define' fase.

2. **Vorm een leidende coalitie:** Een multidisciplinaire werkgroep wordt samengesteld, bestaande uit belangrijke stakeholders met voldoende autoriteit en expertise om de verandering te sturen en de Six Sigma implementatie te borgen.

3. **Ontwikkel een visie en strategie:** De leidende coalitie definieert een heldere visie die Euro Caps positioneert als een leidende, innoverende en lerende organisatie waar kwaliteit integraal onderdeel is van ieders dagelijkse werk.

**Fase 2: Implementatie (12 maanden)**
Deze fase omvat Kotter's stappen 4, 5, en 6, gericht op het daadwerkelijk doorvoeren van de veranderingen.

4. **Communiceer de veranderingsvisie:** De visie wordt breed en herhaaldelijk gecommuniceerd via diverse interne communicatiekanalen om draagvlak te creëren en te onderhouden.

5. **Creëer draagvlak voor actie:** De leidende coalitie identificeert en verwijdert structurele obstakels die de implementatie van Six Sigma en de bredere verandering belemmeren.

6. **Genereer korte termijn successen:** Snelle, zichtbare successen uit kleinschalige Six Sigma projecten worden gevierd en breed gecommuniceerd om motivatie te verhogen.

**Fase 3: Verankering (6 maanden)**
Deze finale fase omvat Kotter's stappen 7 en 8, gericht op duurzame borging.

7. **Consolideer verbeteringen en produceer nog meer verandering:** Na de eerste successen worden de geleerde lessen uit de DMAIC-cycli systematisch gebruikt om verdere verbeteringen te initiëren.

8. **Veranker nieuwe benaderingen in de cultuur:** De nieuwe structuren, processen en gedragingen worden diepgaand geborgd in het beleid, de prestatiebeoordeling en de interne communicatie van Euro Caps.

[Visual van DMAIC en Kotter integratie hier. Een diagram dat toont hoe de DMAIC-cyclus geïntegreerd wordt in elk van Kotter's 8 stappen, met specifieke tijdslijnen en verantwoordelijkheden.]"""
    doc.add_paragraph(kotter_approach_text)

    # 5.2.4 Interventies van de stakeholder
    doc.add_heading('5.2.4 Interventies van de stakeholder', 3)
    stakeholder_interventions_text = """Voor elke stakeholdergroep worden specifieke interventies ontwikkeld om hun betrokkenheid en ondersteuning te waarborgen:

**Directie (Movers):**
- Regelmatige briefings over voortgang en resultaten
- Actieve rol in communicatie naar de organisatie
- Beslissingsbevoegdheid over resource-allocatie

**Middenkader (Mixed Movers/Blockers):**
- Intensieve coaching en training in nieuwe leiderschapsstijl
- Duidelijke verwachtingen en ondersteuning bij rolverandering
- Erkenning en beloning voor succesvolle begeleiding van teams

**Productiemedewerkers (Potential Blockers):**
- Uitgebreide training in Six Sigma methodieken
- Betrokkenheid bij verbeterprojecten vanaf het begin
- Duidelijke communicatie over voordelen en impact op hun werk
- Regelmatige feedback en erkenning van bijdragen

**Ondersteunende afdelingen (Floaters):**
- Informatie over hun rol in het veranderproces
- Mogelijkheden om bij te dragen aan verbeterinitiatieven
- Duidelijke communicatie over verwachtingen"""
    doc.add_paragraph(stakeholder_interventions_text)

    return doc

def add_chapter_6_and_7(doc):
    """Add chapters 6 and 7 to complete the document"""

    # Chapter 6: Communication Plan
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 6: Communicatieplan', 1)

    comm_intro = """Een effectief communicatieplan is cruciaal voor het slagen van de verandering bij Euro Caps. Dit hoofdstuk beschrijft hoe de boodschap van verandering wordt overgebracht aan alle stakeholders, afgestemd op hun specifieke behoeften en verwachtingen."""
    doc.add_paragraph(comm_intro)

    # 6.1 Communication plan overview
    doc.add_heading('6.1 Overzicht communicatieplan', 2)
    comm_plan_text = """Het communicatieplan is gebaseerd op de mensbeelden van De Caluwé en richt zich op verschillende communicatiestijlen voor verschillende stakeholdergroepen:

**Blauwdrukdenken (Rationele benadering):**
Voor de directie en het hoger management wordt gefocust op feiten, cijfers en rationele argumenten. Communicatie gebeurt via formele rapporten, presentaties met concrete data over ROI van Six Sigma implementatie, en strategische documenten.

**Rooddrukdenken (Relationele benadering):**
Voor het middenkader en teamleiders ligt de nadruk op persoonlijke gesprekken, coaching sessies, en het opbouwen van vertrouwen. Communicatie is meer informeel en gericht op het begrijpen van zorgen en het bieden van ondersteuning.

**Geeldrukdenken (Politieke benadering):**
Voor stakeholders met verschillende belangen wordt gefocust op onderhandeling en het vinden van win-win situaties. Communicatie gebeurt via overlegstructuren en werkgroepen waar verschillende perspectieven kunnen worden gedeeld.

**Groendrukdenken (Lerende benadering):**
Voor medewerkers die betrokken zijn bij de implementatie wordt gefocust op leren en ontwikkeling. Communicatie gebeurt via trainingen, workshops, en reflectiesessies.

**Communicatiemiddelen:**
- Maandelijkse nieuwsbrieven met voortgangsupdates
- Kwartaalbijeenkomsten voor alle medewerkers
- Specifieke trainingsmodules per functiegroep
- Intranet met dedicated Six Sigma sectie
- Feedback mechanismen via digitale platforms
- Persoonlijke gesprekken tussen leidinggevenden en teams

**Timing en frequentie:**
- Wekelijkse updates tijdens implementatiefase
- Maandelijkse evaluaties met stakeholders
- Kwartaalrapportages aan directie
- Jaarlijkse evaluatie van het gehele programma"""
    doc.add_paragraph(comm_plan_text)

    # Chapter 7: Conclusion
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 7: Conclusie', 1)

    conclusion_text = """Dit adviesrapport heeft een uitgebreide analyse gepresenteerd van de organisatorische veranderingen die nodig zijn om de Six Sigma implementatie bij Euro Caps te optimaliseren en duurzaam te verankeren. De centrale onderzoeksvraag "Hoe kan Euro Caps op een effectieve en duurzame manier de organisatie inrichten en de cultuur ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma?" is beantwoord door middel van een systematische aanpak gebaseerd op bewezen verandermanagementtheorieën.

De analyse van de huidige situatie toont aan dat Euro Caps een hybride organisatiestructuur heeft die kenmerken vertoont van zowel een machineorganisatie als een innovatieve organisatie. De organisatiecultuur kenmerkt zich door een balans tussen resultaatgerichtheid en mensgerichtheid, met ruimte voor verdere ontwikkeling richting een meer lerende en adaptieve cultuur.

De gewenste situatie schetst een organisatie die de sterke punten van de huidige structuur behoudt, maar meer flexibiliteit en wederzijdse aanpassing integreert. De cultuurverandering richt zich op het versterken van mensgerichtheid, openheid en continue leren, essentieel voor het succes van Six Sigma.

De gekozen veranderstrategie, gebaseerd op Boonstra's ontwikkelingsstrategie en Kotter's achtstappenmodel, biedt een participatieve en gefaseerde aanpak die past bij de cultuur en context van Euro Caps. De implementatie over 21 maanden zorgt voor een geleidelijke maar grondige transformatie.

Het communicatieplan, afgestemd op de verschillende mensbeelden van De Caluwé, waarborgt dat alle stakeholders op een passende manier worden betrokken en geïnformeerd over de veranderingen."""
    doc.add_paragraph(conclusion_text)

    return doc

def add_recommendations_and_appendices(doc):
    """Add recommendations and appendices"""

    # Recommendations
    doc.add_page_break()
    doc.add_heading('Aanbevelingen', 1)

    recommendations_text = """Op basis van de uitgevoerde analyse worden de volgende concrete aanbevelingen gedaan aan Euro Caps:

**Korte termijn (0-6 maanden):**
1. Stel onmiddellijk een leidende coalitie samen bestaande uit CEO Nils Clement, Manager Bedrijfsvoering Servé Bosland, Manager ICT Erik Dekker, en Projectleider Niene Tepe.
2. Ontwikkel een duidelijke visie en communicatiestrategie voor de Six Sigma implementatie.
3. Start met pilotprojecten in de productieafdeling onder leiding van Maik Ritter en Maria Stanić.
4. Implementeer een training- en ontwikkelprogramma voor alle medewerkers.

**Middellange termijn (6-18 maanden):**
1. Rol de Six Sigma methodiek uit naar alle afdelingen.
2. Implementeer nieuwe communicatie- en overlegstructuren.
3. Pas het belonings- en erkenningssysteem aan om continue verbetering te stimuleren.
4. Evalueer en bijstel de organisatiestructuur waar nodig.

**Lange termijn (18+ maanden):**
1. Veranker de nieuwe cultuur in alle HR-processen.
2. Implementeer een systeem voor continue monitoring en verbetering.
3. Ontwikkel Euro Caps tot een lerende organisatie die proactief anticipeert op veranderingen.
4. Evalueer de mogelijkheden voor verdere uitbreiding van kwaliteitsmanagementsystemen.

**Kritische succesfactoren:**
- Consistent leiderschap en commitment van de directie
- Actieve betrokkenheid van alle medewerkers
- Adequate training en ondersteuning
- Effectieve communicatie op alle niveaus
- Regelmatige monitoring en bijsturing van het proces"""
    doc.add_paragraph(recommendations_text)

    # Literature list
    doc.add_page_break()
    doc.add_heading('Literatuurlijst', 1)

    literature_text = """Boonstra, J. J. (2018). Leren veranderen: Een handboek voor de veranderkundige. Amsterdam: Boom uitgevers.

De Caluwé, L., & Vermaak, H. (2009). Leren veranderen: Een handboek voor de veranderkundige. Deventer: Kluwer.

Hofstede, G., Hofstede, G. J., & Minkov, M. (2010). Cultures and Organizations: Software of the Mind. New York: McGraw-Hill.

Kotter, J. P. (1996). Leading Change. Boston: Harvard Business Review Press.

Kübler-Ross, E. (1969). On Death and Dying. New York: Macmillan.

Mintzberg, H. (1983). Structure in Fives: Designing Effective Organizations. Englewood Cliffs: Prentice-Hall.

Thuis, P., & Stuive, R. (2019). Bedrijfskunde (3e editie). Groningen: Noordhoff Uitgevers."""
    doc.add_paragraph(literature_text)

    return doc

def add_all_visuals_to_document():
    """
    Add all available visuals to the document at appropriate locations
    """

    # Load the existing document
    doc_path = 'Adviesrapport_Veranderingsmanagement_VOLLEDIG_GEINTEGREERD_MET_VISUALS.docx'
    doc = Document(doc_path)

    # Define visual mappings - which visual goes where
    visual_mappings = {
        'Visual_1_Boonstra_Veranderstrategieen.png': '[Visual van Boonstra\'s veranderstrategieën hier',
        'Visual_2_Caluwe_Kleurenmodel.png': '[Visual van De Caluwé\'s kleurenmodel hier',
        'Visual_3_Gap_Analyse_Model.png': '[Visual van een Gap-analyse model hier',
        'Visual_4_Hofstede_Cultuurdimensies.png': '[Visual van Hofstede\'s cultuurdimensies hier',
        'Visual_5_Kotter_8_Stappenmodel.png': '[Visual van Kotter\'s 8-stappenmodel hier',
        'Visual_6_Stakeholderanalyse_Matrix.png': '[Visual van een algemeen Stakeholderanalyse model hier',
        'Visual_7_Kubler_Ross_Verandercurve.png': '[Visual van de Verandercurve van Kübler-Ross hier',
        'Visual_8_Beslissingsmatrix_Mintzberg.png': '[Visual van Beslissingsmatrix Mintzberg hier',
        'Visual_9_Boonstra_Beslissingsmatrix.png': '[Visual van Boonstra\'s Beslissingsmatrix hier',
        'Visual_10_DMAIC_Kotter_Integratie.png': '[Visual van DMAIC en Kotter integratie hier'
    }

    # Additional visual mappings for stakeholder analysis
    stakeholder_visuals = {
        'Visual_Stakeholdermatrix.png': '[Visual van Stakeholdermatrix hier',
        'Visual_Stakeholdermap.png': 'Tabel 2: Overzicht stakeholders',
        'Visual_Stakeholdermanagementfasen.png': 'Tabel 3: Categorisatie van de stakeholders'
    }

    # Combine all mappings
    all_visuals = {**visual_mappings, **stakeholder_visuals}

    # Process each paragraph to find visual placeholders
    for i, paragraph in enumerate(doc.paragraphs):
        paragraph_text = paragraph.text

        # Check if this paragraph contains a visual placeholder
        for visual_file, placeholder_text in all_visuals.items():
            if placeholder_text in paragraph_text:
                try:
                    # Check if visual file exists
                    if os.path.exists(visual_file):
                        # Add the visual after this paragraph
                        new_paragraph = doc.paragraphs[i].insert_paragraph_before()
                        run = new_paragraph.runs[0] if new_paragraph.runs else new_paragraph.add_run()

                        # Add the image
                        run.add_picture(visual_file, width=Inches(6))
                        new_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        # Add caption
                        caption_paragraph = doc.paragraphs[i].insert_paragraph_before()
                        caption_paragraph.add_run(f"Figuur: {visual_file.replace('.png', '').replace('Visual_', '').replace('_', ' ')}")
                        caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        print(f"Added visual: {visual_file} at paragraph {i}")

                        # Update the placeholder text to indicate visual was added
                        paragraph.text = paragraph_text.replace(placeholder_text, f"[VISUAL TOEGEVOEGD: {visual_file}]")

                    else:
                        print(f"Visual file not found: {visual_file}")

                except Exception as e:
                    print(f"Error adding visual {visual_file}: {str(e)}")

    # Save the updated document
    output_path = 'Adviesrapport_Veranderingsmanagement_COMPLEET_MET_ALLE_VISUALS.docx'
    doc.save(output_path)
    print(f"Document saved as: {output_path}")

    return output_path

if __name__ == "__main__":
    print("=== Creating Complete Document ===")
    doc = create_complete_document()
    doc = add_chapter_1(doc)
    doc = add_chapter_2(doc)
    doc = add_chapter_2_continued(doc)
    doc = add_chapter_3(doc)
    doc = add_chapter_3_continued(doc)
    doc = add_chapter_4(doc)
    doc = add_chapter_4_continued(doc)
    doc = add_chapter_5_part1(doc)
    doc = add_stakeholder_analysis(doc)
    doc = add_stakeholder_categorization(doc)
    doc = add_chapter_5_part2(doc)
    doc = add_remaining_chapters(doc)
    doc = add_chapter_6_and_7(doc)
    doc = add_recommendations_and_appendices(doc)
    doc.save('Adviesrapport_Veranderingsmanagement_VOLLEDIG_GEINTEGREERD_MET_VISUALS.docx')
    print("Document successfully created with all content and visual indicators!")

    print("\n=== Adding All Visuals ===")
    final_doc = add_all_visuals_to_document()
    print(f"Final document with all visuals: {final_doc}")
    print("\n=== DOCUMENT VOLLEDIG COMPLEET ===")
    print("Alle content uit document (3) is geïntegreerd met alle visual indicaties!")
