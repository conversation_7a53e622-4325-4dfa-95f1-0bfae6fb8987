from docx import Document
from docx.shared import Inches
import os

def insert_visuals():
    doc_path = "Adviesrapport_Veranderingsmanagement_OMGEZET.docx"
    output_path = "Adviesrapport Veranderingsmanagement-<PERSON><PERSON>-1066741.docx"
    visuals = [
        "Visual_Stakeholdermatrix.png",
        "Visual_Stakeholdermanagementfasen.png",
        "Visual_Stakeholdermap.png",
        "Visual_Kotter8Stappen.png"
    ]
    doc = Document(doc_path)
    visual_idx = 0
    for para in doc.paragraphs:
        if '[VISUAL HIER]' in para.text and visual_idx < len(visuals):
            para.clear()
            if os.path.exists(visuals[visual_idx]):
                run = para.add_run()
                run.add_picture(visuals[visual_idx], width=Inches(5.5))
                para.alignment = 1
                visual_idx += 1
    doc.save(output_path)
    print("Visuals ingevoegd en definitief document opgeslagen.")

if __name__ == "__main__":
    insert_visuals() 