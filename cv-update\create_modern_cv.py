from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
import os

def create_modern_cv():
    # Create a new document
    doc = Document()

    # Set document margins (similar to Husna's CV)
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.8)
        section.bottom_margin = Inches(0.8)
        section.left_margin = Inches(0.8)
        section.right_margin = Inches(0.8)

    # Header with name (following <PERSON><PERSON><PERSON>'s style)
    header = doc.add_paragraph()
    header.alignment = WD_ALIGN_PARAGRAPH.LEFT

    # Name (large, bold, similar to <PERSON><PERSON><PERSON>'s style)
    name_run = header.add_run('<PERSON><PERSON>')
    name_run.font.size = Pt(20)
    name_run.font.bold = True
    name_run.font.color.rgb = RGBColor(0, 0, 0)  # Black like <PERSON>sna's

    # Contact info (following <PERSON><PERSON><PERSON>'s format exactly)
    contact = doc.add_paragraph()
    contact.alignment = WD_ALIGN_PARAGRAPH.LEFT
    contact_run = contact.add_run('<PERSON>hinnenbaan 2, 3077 JJ Rotterdam | 06-28411923 | 010-4113222 | <EMAIL>')
    contact_run.font.size = Pt(11)
    contact_run.font.color.rgb = RGBColor(0, 0, 0)

    # Add space
    doc.add_paragraph()

    # Persoonlijke gegevens sectie (BOVENAAN zoals in originele CV)
    personal_heading = doc.add_paragraph()
    personal_heading_run = personal_heading.add_run('Persoonlijke gegevens')
    personal_heading_run.font.size = Pt(14)
    personal_heading_run.font.bold = True
    personal_heading_run.font.color.rgb = RGBColor(0, 0, 0)

    # Persoonlijke gegevens met consistente layout (zoals andere secties)
    personal_data = [
        ('Naam', 'Shuja'),
        ('Achternaam', 'Schadon'),
        ('Adres', 'Schinnenbaan 2'),
        ('Postcode', '3077 JJ Rotterdam'),
        ('Geboren', '11 April 1998'),
        ('Nationaliteit', 'Nederlandse'),
        ('GSM', '06-28411923'),
        ('E-mail', '<EMAIL>')
    ]

    for label, value in personal_data:
        personal_para = doc.add_paragraph()
        personal_para.add_run(f'· {label}: {value}')
        personal_para.paragraph_format.left_indent = Inches(0.2)

    doc.add_paragraph()
    
    # Professional Summary (following Husna's style)
    summary_heading = doc.add_paragraph()
    summary_heading_run = summary_heading.add_run('Persoonlijk profiel')
    summary_heading_run.font.size = Pt(14)
    summary_heading_run.font.bold = True
    summary_heading_run.font.color.rgb = RGBColor(0, 0, 0)

    summary_text = doc.add_paragraph()
    summary_text.add_run('ICT-professional met uitgebreide ervaring in systeembeheer, netwerkinfrastructuur en cybersecurity. Afgestudeerd als System Devices Expert (MBO niveau 4) met praktijkervaring in firewall-configuratie, server-installaties en IT-ondersteuning. Momenteel bezig met HBO-studie en cybersecurity-certificeringen om expertise verder uit te breiden.')
    summary_text.paragraph_format.space_after = Pt(12)
    
    # Education (van laag naar hoog zoals gevraagd)
    education_heading = doc.add_paragraph()
    education_heading_run = education_heading.add_run('Opleiding')
    education_heading_run.font.size = Pt(14)
    education_heading_run.font.bold = True
    education_heading_run.font.color.rgb = RGBColor(0, 0, 0)

    # VMBO (laagste niveau)
    vmbo_para = doc.add_paragraph()
    vmbo_run = vmbo_para.add_run('VMBO | 2013 – 2017 | Olympia College Rotterdam')
    vmbo_run.font.bold = True
    vmbo_detail = doc.add_paragraph()
    vmbo_detail.add_run('· Richting: techniek')
    vmbo_detail.paragraph_format.left_indent = Inches(0.2)

    # MBO 1
    mbo1_para = doc.add_paragraph()
    mbo1_run = mbo1_para.add_run('MBO niveau 1 | Albeda College Rotterdam')
    mbo1_run.font.bold = True
    mbo1_detail = doc.add_paragraph()
    mbo1_detail.add_run('· Opleiding: Entree opleiding Assistent installatie/Constructie techniek')
    mbo1_detail.paragraph_format.left_indent = Inches(0.2)

    # MBO 2
    mbo2_para = doc.add_paragraph()
    mbo2_run = mbo2_para.add_run('MBO niveau 2 | Techniek College Rotterdam')
    mbo2_run.font.bold = True
    mbo2_detail = doc.add_paragraph()
    mbo2_detail.add_run('· Opleiding: Medewerker ICT 1e jaars niveau 2')
    mbo2_detail.paragraph_format.left_indent = Inches(0.2)

    # MBO 3
    mbo3_para = doc.add_paragraph()
    mbo3_run = mbo3_para.add_run('MBO niveau 3 | 2018 – 2020 | Techniek College Rotterdam')
    mbo3_run.font.bold = True
    mbo3_detail = doc.add_paragraph()
    mbo3_detail.add_run('· Opleiding: Medewerker ICT 2e jaars niveau 3 (diploma behaald 18-09-2020)')
    mbo3_detail.paragraph_format.left_indent = Inches(0.2)

    # MBO 4
    mbo4_para = doc.add_paragraph()
    mbo4_run = mbo4_para.add_run('MBO niveau 4 | 2020 – 2023 | Techniek College Rotterdam')
    mbo4_run.font.bold = True
    mbo4_detail = doc.add_paragraph()
    mbo4_detail.add_run('· Opleiding: System Devices Expert ICT 3e jaars niveau 4 (diploma behaald 18-09-2023)')
    mbo4_detail.paragraph_format.left_indent = Inches(0.2)

    # HBO (hoogste niveau)
    hbo_para = doc.add_paragraph()
    hbo_run = hbo_para.add_run('HBO jaar 1 | 2024 – heden | Hogeschool Rotterdam')
    hbo_run.font.bold = True
    hbo_detail = doc.add_paragraph()
    hbo_detail.add_run('· Opleiding: bedrijfskunde (propedeuse behaald 02-10-2025)')
    hbo_detail.paragraph_format.left_indent = Inches(0.2)

    doc.add_paragraph()
    
    # Vaardigheden section (following Husna's style)
    skills_heading = doc.add_paragraph()
    skills_heading_run = skills_heading.add_run('Vaardigheden')
    skills_heading_run.font.size = Pt(14)
    skills_heading_run.font.bold = True
    skills_heading_run.font.color.rgb = RGBColor(0, 0, 0)

    skills_list = [
        'Probleemoplossend', 'Technisch inzicht', 'Communicatief', 'Doelgericht',
        'Analytisch', 'Leergierig', 'Teamwork', 'Zelfstandig werken',
        'Klantgericht', 'Nauwkeurig'
    ]

    for skill in skills_list:
        skill_para = doc.add_paragraph()
        skill_para.add_run(f'· {skill}')
        skill_para.paragraph_format.left_indent = Inches(0.2)

    doc.add_paragraph()

    # Softwarekundige vaardigheden (following Husna's style)
    software_heading = doc.add_paragraph()
    software_heading_run = software_heading.add_run('Softwarekundige vaardigheden')
    software_heading_run.font.size = Pt(14)
    software_heading_run.font.bold = True
    software_heading_run.font.color.rgb = RGBColor(0, 0, 0)

    software_list = [
        'Microsoft Word', 'Microsoft Excel', 'Microsoft PowerPoint',
        'Windows 7/10/11', 'macOS', 'AutoCAD 2014 & 2017',
        '3D Studio 2013 & 2017', 'PowerShell', 'RMM Datto',
        'Sophos Firewall', 'PDMS (basis)', 'Isometrische tekeningen'
    ]

    for software in software_list:
        software_para = doc.add_paragraph()
        software_para.add_run(f'· {software}')
        software_para.paragraph_format.left_indent = Inches(0.2)

    doc.add_paragraph()

    # Professional Experience
    experience_heading = doc.add_paragraph()
    experience_heading_run = experience_heading.add_run('Werkervaring')
    experience_heading_run.font.size = Pt(14)
    experience_heading_run.font.bold = True
    experience_heading_run.font.color.rgb = RGBColor(0, 0, 0)
    
    # !Bien experience (following Husna's format)
    bien_para = doc.add_paragraph()
    bien_title = bien_para.add_run('ICT Stagiair | !Bien |')
    bien_title.font.bold = True
    bien_para.add_run(' Januari 2023 – Juni 2023')

    bien_tasks_header = doc.add_paragraph()
    bien_tasks_header.add_run('· Werkzaamheden:')
    bien_tasks_header.paragraph_format.left_indent = Inches(0.2)

    bien_tasks = [
        'Firewall installeren op locatie',
        'Met PowerShell applicaties updaten met standaard installatiebestanden',
        'Laptop installeren met meegeleverde procedure van de klant',
        'Netwerktekeningen maken',
        'Router configureren voor remote access',
        'Server geïnstalleerd voor klanten',
        'Dagelijkse back-up controle via RMM Datto'
    ]

    for task in bien_tasks:
        task_para = doc.add_paragraph()
        task_para.add_run(f'o {task}')
        task_para.paragraph_format.left_indent = Inches(0.4)

    # NMC Maritime experience
    nmc_para = doc.add_paragraph()
    nmc_title = nmc_para.add_run('Technisch Stagiair | NMC Maritime Technology |')
    nmc_title.font.bold = True
    nmc_para.add_run(' Mei 2014 – Mei 2015')

    nmc_tasks_header = doc.add_paragraph()
    nmc_tasks_header.add_run('· Werkzaamheden:')
    nmc_tasks_header.paragraph_format.left_indent = Inches(0.2)

    nmc_tasks = [
        'Magazijn assistent',
        'Oefenen met lassen en electrode lassen',
        'Werkstukken gemaakt',
        'Samenwerken met collega\'s op de sectie',
        'Kleine IT werkzaamheden uitgevoerd',
        'Kantoren indelen'
    ]

    for task in nmc_tasks:
        task_para = doc.add_paragraph()
        task_para.add_run(f'o {task}')
        task_para.paragraph_format.left_indent = Inches(0.4)

    # Scheepswerf Slob experience
    slob_para = doc.add_paragraph()
    slob_title = slob_para.add_run('Technisch Stagiair | Scheepswerf Slob |')
    slob_title.font.bold = True
    slob_para.add_run(' Oktober 2013 – Mei 2014')

    slob_tasks_header = doc.add_paragraph()
    slob_tasks_header.add_run('· Werkzaamheden:')
    slob_tasks_header.paragraph_format.left_indent = Inches(0.2)

    slob_tasks = [
        'Balken glad slijpen',
        'Oefenen met lassen en TIG lassen',
        'T-balken schuine kant maken aan de hand van tekening',
        'Toezicht houden op las robot',
        'Platen op maat zagen volgens tekening'
    ]

    for task in slob_tasks:
        task_para = doc.add_paragraph()
        task_para.add_run(f'o {task}')
        task_para.paragraph_format.left_indent = Inches(0.4)

    doc.add_paragraph()
    
    # Current Certifications (following Husna's style)
    cert_heading = doc.add_paragraph()
    cert_heading_run = cert_heading.add_run('Lopende certificeringen')
    cert_heading_run.font.size = Pt(14)
    cert_heading_run.font.bold = True
    cert_heading_run.font.color.rgb = RGBColor(0, 0, 0)

    cert_items = [
        'The Complete Cyber Security Course: Hackers Exposed!',
        'The Complete Certified in Cybersecurity (CC) course ISC2 \'23',
        'CompTIA Security+ (SY0-601) Complete Course & Exam',
        'Cyber Security: From Beginner to Expert (2023)',
        'VCA Basis certificering'
    ]

    for cert in cert_items:
        cert_para = doc.add_paragraph()
        cert_para.add_run(f'· {cert}')
        cert_para.paragraph_format.left_indent = Inches(0.2)

    doc.add_paragraph()

    # Additional Information section
    additional_heading = doc.add_paragraph()
    additional_heading_run = additional_heading.add_run('Aanvullende informatie')
    additional_heading_run.font.size = Pt(14)
    additional_heading_run.font.bold = True
    additional_heading_run.font.color.rgb = RGBColor(0, 0, 0)

    additional_items = [
        'Ervaring met hardware assemblage en PC-bouw',
        'Troubleshooting van Windows en macOS systemen',
        'Netwerkinfrastructuur en firewall-configuratie',
        'Backup-monitoring en systeembeheer',
        'Technische documentatie en isometrische tekeningen',
        'Klantgerichte IT-ondersteuning'
    ]

    for item in additional_items:
        item_para = doc.add_paragraph()
        item_para.add_run(f'· {item}')
        item_para.paragraph_format.left_indent = Inches(0.2)

    return doc

# Create and save the document
if __name__ == "__main__":
    cv_folder = r"C:\Users\<USER>\Documents\augment-projects\Americaps\cv-update"

    # Create the modern CV
    doc = create_modern_cv()

    # Save the document
    output_path = os.path.join(cv_folder, "CV Shuja Schadon - Finale Versie 2025.docx")
    doc.save(output_path)

    print(f"Moderne CV succesvol aangemaakt: {output_path}")
    print("\nKenmerken van de nieuwe CV (finale versie):")
    print("✓ Persoonlijke gegevens BOVENAAN geplaatst")
    print("✓ Consistente layout voor alle secties")
    print("✓ Layout en stijl gebaseerd op Husna's CV")
    print("✓ Opleiding van laag naar hoog geordend (VMBO → HBO)")
    print("✓ Alle originele contactgegevens en details behouden")
    print("✓ Werkervaring gestructureerd met bullet points")
    print("✓ Vaardigheden en software apart georganiseerd")
    print("✓ Autolessen sectie verwijderd")
    print("✓ Talenkennis sectie verwijderd")
    print("✓ Consistente formatting en typografie")
    print("✓ Professionele structuur voor bedrijven")
