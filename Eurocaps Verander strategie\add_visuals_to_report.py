import os
import docx
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

def add_visuals_to_report():
    # Pad naar het rapport
    report_path = "Adviesrapport Veranderingsmanagement-<PERSON><PERSON>-1066741.docx"
    visuals_folder = "Exported_Slides"
    
    # Controleer of het rapport bestaat
    if not os.path.exists(report_path):
        print(f"Rapport niet gevonden: {report_path}")
        return
    
    print(f"Rapport gevonden: {report_path}")
    print(f"Visuals folder: {visuals_folder}")
    
    # Open het document
    doc = docx.Document(report_path)
    
    # Lijst van belangrijke visuals om toe te voegen
    important_visuals = [
        # Stakeholderanalyse
        ("4. Stakeholderanalyse/slide/Slide1.PNG", "Stakeholderanalyse - Overzicht"),
        ("4. Stakeholderanalyse/slide/Slide2.PNG", "Stakeholderanalyse - Definitie"),
        ("4. Stakeholderanalyse/slide/Slide3.PNG", "Stakeholderanalyse - Categorisatie"),
        ("4. Stakeholderanalyse/slide/Slide4.PNG", "Stakeholderanalyse - Matrix"),
        ("4. Stakeholderanalyse/slide/Slide5.PNG", "Stakeholderanalyse - Communicatiestrategie"),
        
        # Kotter model
        ("2. Kotter/slide/Slide1.PNG", "Kotter 8-stappenmodel - Overzicht"),
        ("2. Kotter/slide/Slide2.PNG", "Kotter - Stap 1: Urgentiebesef"),
        ("2. Kotter/slide/Slide3.PNG", "Kotter - Stap 2: Coalitie vormen"),
        ("2. Kotter/slide/Slide4.PNG", "Kotter - Stap 3: Visie ontwikkelen"),
        ("2. Kotter/slide/Slide5.PNG", "Kotter - Stap 4: Visie communiceren"),
        ("2. Kotter/slide/Slide6.PNG", "Kotter - Stap 5: Obstakels wegnemen"),
        ("2. Kotter/slide/Slide7.PNG", "Kotter - Stap 6: Korte termijn successen"),
        ("2. Kotter/slide/Slide8.PNG", "Kotter - Stap 7: Verandering verankeren"),
        ("2. Kotter/slide/Slide9.PNG", "Kotter - Stap 8: Nieuwe cultuur"),
        
        # Caluwe model
        ("5. Caluwe/slide/Slide1.PNG", "Caluwé Kleurenmodel - Overzicht"),
        ("5. Caluwe/slide/Slide2.PNG", "Caluwé - Blauw denken"),
        ("5. Caluwe/slide/Slide3.PNG", "Caluwé - Rood denken"),
        ("5. Caluwe/slide/Slide4.PNG", "Caluwé - Geel denken"),
        ("5. Caluwe/slide/Slide5.PNG", "Caluwé - Groen denken"),
        ("5. Caluwe/slide/Slide6.PNG", "Caluwé - Wit denken"),
        
        # Veranderaanpak
        ("2. Veranderaanpak/slide/Slide1.PNG", "Veranderaanpak - Overzicht"),
        ("2. Veranderaanpak/slide/Slide2.PNG", "Veranderaanpak - Methoden"),
        
        # Communicatieplan
        ("6. Communicatieplan/slide/Slide1.PNG", "Communicatieplan - Overzicht"),
        ("6. Communicatieplan/slide/Slide2.PNG", "Communicatieplan - Strategie"),
        
        # Interventies
        ("6. Interventies/slide/Slide1.PNG", "Interventies - Overzicht"),
        ("6. Interventies/slide/Slide2.PNG", "Interventies - Typen"),
    ]
    
    # Voeg een sectie toe voor visuals
    doc.add_heading('Bijlagen - Visuals', level=1)
    
    # Voeg elke visual toe
    for visual_path, title in important_visuals:
        full_path = os.path.join(visuals_folder, visual_path)
        if os.path.exists(full_path):
            # Voeg titel toe
            doc.add_heading(title, level=2)
            
            # Voeg afbeelding toe
            doc.add_picture(full_path, width=Inches(6))
            
            # Voeg wat ruimte toe
            doc.add_paragraph()
            print(f"Toegevoegd: {title}")
        else:
            print(f"Niet gevonden: {full_path}")
    
    # Sla het document op
    doc.save(report_path)
    print(f"\nVisuals toegevoegd aan: {report_path}")

if __name__ == "__main__":
    add_visuals_to_report() 