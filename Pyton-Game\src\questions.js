// Python Quiz Questions Database - Gebaseerd op Syllabus
export const questionDatabase = {
    beginner: [
        {
            id: 1,
            type: "code-input",
            category: "Variabelen en Datatypes",
            question: "Maak variabelen voor: naam (string), leeftijd (integer), lengte (float), en is_student (boolean). Print alle variabelen.",
            expectedKeywords: ["naam", "leeftijd", "lengte", "is_student", "print", "="],
            solution: `naam = "Alice"
leeftijd = 30
lengte = 1.75
is_student = True
print(naam)
print(leeftijd)
print(lengte)
print(is_student)`,
            explanation: {
                correct: "Perfect! Je hebt alle basis datatypes gebruikt: string (tekst), integer (geheel getal), float (decimaal getal), en boolean (True/False).",
                incorrect: "Gebruik aanhalingstekens voor strings, gewone getallen voor integers, decimale getallen voor floats, en True/False voor booleans.",
                hints: [
                    "String: naam = \"Alice\"",
                    "Integer: leeftijd = 30",
                    "Float: lengte = 1.75", 
                    "Boolean: is_student = True"
                ]
            }
        },
        {
            id: 2,
            type: "code-input",
            category: "Print en Strings",
            question: "Print de tekst 'Hallo, wereld!' en gebruik daarna print() om je eigen naam en leeftijd te tonen in één zin.",
            expectedKeywords: ["print", "Hallo", "wereld"],
            solution: `print("Hallo, wereld!")
naam = "Alice"
leeftijd = 25
print("Mijn naam is", naam, "en ik ben", leeftijd, "jaar oud")`,
            explanation: {
                correct: "Goed gedaan! Je hebt print() gebruikt voor tekst en variabelen gecombineerd in één print statement.",
                incorrect: "Gebruik print() voor tekst tussen aanhalingstekens. Je kunt meerdere dingen printen door ze te scheiden met komma's.",
                hints: [
                    "print(\"Hallo, wereld!\") voor de eerste regel",
                    "Maak variabelen voor naam en leeftijd",
                    "Gebruik komma's om meerdere dingen te printen",
                    "print(\"tekst\", variabele, \"meer tekst\")"
                ]
            }
        },
        {
            id: 3,
            type: "code-input",
            category: "Rekenkundige Operatoren",
            question: "Maak twee variabelen a=15 en b=4. Bereken en print: optelling, aftrekking, vermenigvuldiging, gewone deling, gehele deling en modulo.",
            expectedKeywords: ["a", "b", "+", "-", "*", "/", "//", "%", "print"],
            solution: `a = 15
b = 4
print("Optelling:", a + b)
print("Aftrekking:", a - b)
print("Vermenigvuldiging:", a * b)
print("Gewone deling:", a / b)
print("Gehele deling:", a // b)
print("Modulo:", a % b)`,
            explanation: {
                correct: "Perfect! Je hebt alle rekenkundige operatoren gebruikt. Let op: / geeft een float, // geeft alleen het gehele deel, % geeft de rest.",
                incorrect: "Gebruik alle operatoren: + - * / // %. Let op het verschil tussen / (3.75) en // (3).",
                hints: [
                    "a = 15, b = 4",
                    "/ is gewone deling (3.75)",
                    "// is gehele deling (3)", 
                    "% is modulo/rest (3)"
                ]
            }
        },
        {
            id: 4,
            type: "code-input",
            category: "Lists Basis",
            question: "Maak een lijst met getallen [1, 2, 3]. Print de lijst, voeg het getal 4 toe met append(), en print de lijst opnieuw.",
            expectedKeywords: ["list", "[", "]", "append", "print"],
            solution: `getallen = [1, 2, 3]
print("Originele lijst:", getallen)
getallen.append(4)
print("Na toevoegen:", getallen)`,
            explanation: {
                correct: "Goed! Je hebt een lijst gemaakt met vierkante haken en append() gebruikt om een element toe te voegen.",
                incorrect: "Maak een lijst met [1, 2, 3] en gebruik .append(4) om een element toe te voegen.",
                hints: [
                    "lijst = [1, 2, 3] om een lijst te maken",
                    "lijst.append(4) om 4 toe te voegen",
                    "Print de lijst voor en na append()"
                ]
            }
        },
        {
            id: 5,
            type: "code-input",
            category: "For Loop Basis",
            question: "Schrijf een for-loop die de getallen 1 tot en met 5 print. Gebruik range().",
            expectedKeywords: ["for", "range", "print"],
            solution: `for i in range(1, 6):
    print(i)`,
            explanation: {
                correct: "Perfect! Je hebt range(1, 6) gebruikt om getallen 1 tot en met 5 te krijgen. Let op: range(1, 6) geeft 1,2,3,4,5.",
                incorrect: "Gebruik for i in range(1, 6): en print(i) in de loop. Range eindigt altijd één voor het laatste getal.",
                hints: [
                    "for i in range(1, 6):",
                    "range(1, 6) geeft 1,2,3,4,5",
                    "Vergeet de dubbele punt :",
                    "print(i) met indentatie"
                ]
            }
        },
        {
            id: 6,
            type: "code-input",
            category: "If Statements",
            question: "Maak een variabele 'getal' met waarde 10. Schrijf een if-statement dat print 'Groot' als het getal groter is dan 5, anders print 'Klein'.",
            expectedKeywords: ["getal", "if", "else", "print", ">"],
            solution: `getal = 10
if getal > 5:
    print("Groot")
else:
    print("Klein")`,
            explanation: {
                correct: "Uitstekend! Je hebt een if-else statement gebruikt met een vergelijking. Let op de dubbele punt en indentatie.",
                incorrect: "Gebruik if getal > 5: gevolgd door print(\"Groot\"), dan else: gevolgd door print(\"Klein\"). Vergeet de indentatie niet.",
                hints: [
                    "getal = 10",
                    "if getal > 5:",
                    "    print(\"Groot\")",
                    "else:",
                    "    print(\"Klein\")"
                ]
            }
        },
        {
            id: 7,
            type: "code-input",
            category: "String Methoden",
            question: "Maak een string 'tekst' met waarde 'python'. Print de tekst in hoofdletters en tel hoeveel letters 'p' erin zitten.",
            expectedKeywords: ["tekst", "upper", "count", "print"],
            solution: `tekst = "python"
print("Hoofdletters:", tekst.upper())
print("Aantal p:", tekst.count('p'))`,
            explanation: {
                correct: "Goed gedaan! Je hebt .upper() gebruikt voor hoofdletters en .count() om karakters te tellen.",
                incorrect: "Gebruik .upper() om naar hoofdletters te converteren en .count('p') om te tellen.",
                hints: [
                    "tekst = \"python\"",
                    "tekst.upper() voor hoofdletters",
                    "tekst.count('p') om p's te tellen"
                ]
            }
        },
        {
            id: 8,
            type: "code-input",
            category: "Eenvoudige Functie",
            question: "Schrijf een functie 'groet' die geen parameters heeft en 'Hallo!' print. Roep de functie aan.",
            expectedKeywords: ["def", "groet", "print", "Hallo"],
            solution: `def groet():
    print("Hallo!")

groet()`,
            explanation: {
                correct: "Perfect! Je hebt een eenvoudige functie gemaakt met def, print gebruikt, en de functie aangeroepen.",
                incorrect: "Gebruik def groet(): om de functie te definiëren, print(\"Hallo!\") erin, en groet() om aan te roepen.",
                hints: [
                    "def groet(): om functie te definiëren",
                    "print(\"Hallo!\") in de functie",
                    "groet() om de functie aan te roepen"
                ]
            }
        },
        {
            id: 9,
            type: "code-input",
            category: "While Loop Basis",
            question: "Schrijf een while-loop die de getallen 1, 2, 3 print. Start met i=1 en stop als i groter is dan 3.",
            expectedKeywords: ["while", "i", "print", "<=", "+="],
            solution: `i = 1
while i <= 3:
    print(i)
    i += 1`,
            explanation: {
                correct: "Uitstekend! Je hebt een while-loop gebruikt met een teller variabele. Vergeet nooit i += 1 anders krijg je een oneindige loop!",
                incorrect: "Start met i = 1, gebruik while i <= 3:, print i, en verhoog i met i += 1.",
                hints: [
                    "i = 1 om te beginnen",
                    "while i <= 3: voor de conditie",
                    "print(i) om het getal te tonen",
                    "i += 1 om i te verhogen"
                ]
            }
        },
        {
            id: 10,
            type: "code-input",
            category: "Variabelen Wisselen",
            question: "Maak twee variabelen x=5 en y=10. Wissel hun waarden zonder een extra variabele te gebruiken. Print x en y voor en na het wisselen.",
            expectedKeywords: ["x", "y", "=", "print"],
            solution: `x = 5
y = 10
print("Voor wisselen: x =", x, "y =", y)
x, y = y, x
print("Na wisselen: x =", x, "y =", y)`,
            explanation: {
                correct: "Uitstekend! Je hebt de Python tuple unpacking gebruikt om variabelen te wisselen. Dit is een elegante manier zonder extra variabele.",
                incorrect: "Gebruik x, y = y, x om de waarden te wisselen. Dit is een speciale Python feature.",
                hints: [
                    "x = 5 en y = 10 om te beginnen",
                    "Print eerst de originele waarden",
                    "Gebruik x, y = y, x om te wisselen",
                    "Print daarna de nieuwe waarden"
                ]
            }
        }
    ],
    
    intermediate: [
        {
            id: 11,
            type: "code-input",
            category: "Dictionary Basis",
            question: "Maak een dictionary 'persoon' met keys 'naam' en 'leeftijd'. Print beide waarden apart.",
            expectedKeywords: ["persoon", "{", "}", "naam", "leeftijd", "print"],
            solution: `persoon = {"naam": "Jan", "leeftijd": 25}
print("Naam:", persoon["naam"])
print("Leeftijd:", persoon["leeftijd"])`,
            explanation: {
                correct: "Perfect! Je hebt een dictionary gemaakt met accolades {} en waarden opgehaald met vierkante haken [].",
                incorrect: "Gebruik accolades {} voor dictionaries en vierkante haken [] om waarden op te halen.",
                hints: [
                    "persoon = {\"naam\": \"Jan\", \"leeftijd\": 25}",
                    "persoon[\"naam\"] om naam op te halen",
                    "persoon[\"leeftijd\"] om leeftijd op te halen"
                ]
            }
        },
        {
            id: 12,
            type: "code-input",
            category: "Functie met Parameters",
            question: "Schrijf een functie 'som' die twee parameters a en b neemt en hun som teruggeeft. Test met som(5, 3).",
            expectedKeywords: ["def", "som", "return", "+", "print"],
            solution: `def som(a, b):
    return a + b

resultaat = som(5, 3)
print("Som:", resultaat)`,
            explanation: {
                correct: "Uitstekend! Je hebt een functie met parameters gemaakt en return gebruikt om een waarde terug te geven.",
                incorrect: "Gebruik def som(a, b): om de functie te definiëren en return a + b om de som terug te geven.",
                hints: [
                    "def som(a, b): voor functie met parameters",
                    "return a + b om som terug te geven",
                    "som(5, 3) om functie aan te roepen"
                ]
            }
        }
    ],
    
    advanced: [
        {
            id: 21,
            type: "code-input",
            category: "Try-Except Basis",
            question: "Schrijf een try-except blok dat probeert 10 te delen door 0. Print 'Fout!' als er een error is.",
            expectedKeywords: ["try", "except", "print", "/"],
            solution: `try:
    resultaat = 10 / 0
    print(resultaat)
except:
    print("Fout!")`,
            explanation: {
                correct: "Goed! Je hebt try-except gebruikt om errors af te handelen. Dit voorkomt dat je programma crasht.",
                incorrect: "Gebruik try: voor de code die een fout kan geven, en except: om de fout af te handelen.",
                hints: [
                    "try: om code te proberen",
                    "10 / 0 geeft een ZeroDivisionError",
                    "except: om de fout af te handelen",
                    "print(\"Fout!\") in het except blok"
                ]
            }
        }
    ]
};

// Question categories for study recommendations
export const categories = {
    "Variabelen en Datatypes": "Bestudeer datatypes, variabele toewijzing en type conversies",
    "Print en Strings": "Oefen met print statements en string operaties",
    "Rekenkundige Operatoren": "Oefen met rekenkundige, vergelijkings- en logische operatoren",
    "Lists Basis": "Bestudeer list methoden, indexing en slicing",
    "For Loop Basis": "Oefen met for loops en range() functie",
    "If Statements": "Bestudeer if/elif/else statements en boolean logic",
    "String Methoden": "Leer string methoden en string formatting",
    "Eenvoudige Functie": "Leer functie definitie en aanroepen",
    "While Loop Basis": "Oefen met while loops en loop controle",
    "Variabelen Wisselen": "Bestudeer tuple unpacking en variabele operaties",
    "Dictionary Basis": "Leer dictionary methoden en key-value pairs",
    "Functie met Parameters": "Bestudeer functie parameters en return statements",
    "Try-Except Basis": "Bestudeer try/except/finally blokken"
};

// Get questions for specific difficulty level
export function getQuestions(difficulty) {
    const questions = questionDatabase[difficulty] || questionDatabase.beginner;
    return shuffleArray([...questions]); // Return shuffled copy
}

// Shuffle array utility function
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Get study recommendations based on wrong answers
export function getStudyRecommendations(wrongCategories) {
    return wrongCategories.map(category => categories[category] || `Bestudeer meer over ${category}`);
}
