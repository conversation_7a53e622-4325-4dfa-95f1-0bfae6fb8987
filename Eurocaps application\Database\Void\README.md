# EuroCaps Ordering System Database

This folder contains CSV files that serve as the database for the EuroCaps Ordering System prototype. These files can be imported into Excel and then used as data sources in PowerApps.

## Files Included

1. **Customers.csv** - Customer information
2. **Products.csv** - Product catalog
3. **Orders.csv** - Order header information
4. **OrderItems.csv** - Order line items

## How to Use with PowerApps

### Option 1: Use CSV Files Directly
1. Upload these CSV files to OneDrive or SharePoint
2. In PowerApps, add a data connection to each CSV file
3. Use the tables in your app

### Option 2: Create an Excel Workbook
1. Open Excel
2. Create a new workbook
3. Import each CSV file into a separate sheet
4. Format each sheet as a table (Insert > Table)
5. Name each table appropriately (e.g., "Customers", "Products", etc.)
6. Save the Excel file
7. Upload to OneDrive or SharePoint
8. Connect to the Excel file in PowerApps

## Data Structure

### Customers Table
- CustomerID (Number): Unique identifier for each customer
- CustomerName (Text): Company name
- ContactPerson (Text): Primary contact name
- Email (Text): Contact email address
- Phone (Text): Contact phone number
- Address (Text): Physical address

### Products Table
- ProductID (Number): Unique identifier for each product
- ProductName (Text): Name of the product
- ProductType (Text): Category (espresso, lungo, ristretto, flavored)
- PackageSize (Number): Number of capsules per package (10, 20, 44)
- Description (Text): Product description
- Price (Number): Unit price

### Orders Table
- OrderID (Number): Unique identifier for each order
- OrderNumber (Text): Human-readable order reference
- CustomerID (Number): Foreign key to Customers table
- OrderDate (Date): Date the order was placed
- DeliveryDate (Date): Requested delivery date
- Status (Text): Order status (new, processing, shipped, delivered, cancelled)
- Notes (Text): Additional order information

### OrderItems Table
- OrderItemID (Number): Unique identifier for each order item
- OrderID (Number): Foreign key to Orders table
- ProductID (Number): Foreign key to Products table
- Quantity (Number): Number of units ordered

## Sample Data

The CSV files contain sample data that can be used for testing and demonstration purposes. In a production environment, you would replace this with real customer and product data.

## Updating the Data

For the prototype, you can manually edit the CSV files or Excel workbook to add, modify, or delete records. In a production environment, you would implement proper data management through PowerApps forms and commands.
