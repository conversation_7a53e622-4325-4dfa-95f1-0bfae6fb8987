# ************************************************************************************************
# Account Creation Screen for EuroCaps Ordering System
# Volledig functioneel account registratie systeem met validatie
# Gebaseerd op mockup specificaties en database structuur
# ************************************************************************************************

# SCREEN PROPERTIES
Account_Creation_Screen As screen:
    Fill: =RGBA(245, 245, 245, 1)  // Background color #f5f5f5
    LoadingSpinnerColor: =RGBA(74, 111, 165, 1)  // Header color #4a6fa5
    
    # OnVisible Event - Initialize form and validation variables
    OnVisible: |
        =// Initialize form variables
        Set(varUsernameValid, false);
        Set(varEmailValid, false);
        Set(varPasswordValid, false);
        Set(varPasswordMatch, false);
        Set(varFormValid, false);
        Set(varUsernameAvailable, true);
        Set(varEmailAvailable, true);
        
        // Load existing user accounts for validation
        ClearCollect(colUserAccounts,
            {UserID: 1, Username: "admin", Email: "<EMAIL>", FirstName: "System", LastName: "Administrator"},
            {UserID: 2, Username: "manager", Email: "<EMAIL>", FirstName: "Operations", LastName: "Manager"},
            {UserID: 3, Username: "sales", Email: "<EMAIL>", FirstName: "Sales", LastName: "Representative"},
            {UserID: 4, Username: "david_lee", Email: "<EMAIL>", FirstName: "David", LastName: "Lee"},
            {UserID: 5, Username: "john_smith", Email: "<EMAIL>", FirstName: "John", LastName: "Smith"}
        );
        
        // Initialize company types
        ClearCollect(colCompanyTypes,
            {Value: "Retail", Text: "Retail"},
            {Value: "Wholesale", Text: "Wholesale"},
            {Value: "Restaurant", Text: "Restaurant"},
            {Value: "Office", Text: "Office"}
        );
        
        // Initialize countries
        ClearCollect(colCountries,
            {Value: "Netherlands", Text: "Netherlands"},
            {Value: "Belgium", Text: "Belgium"},
            {Value: "Germany", Text: "Germany"},
            {Value: "France", Text: "France"}
        )

# HEADER BAR
HeaderBar As rectangle:
    Fill: =RGBA(74, 111, 165, 1)  // Header color #4a6fa5
    Height: =60
    Width: =Parent.Width
    X: =0
    Y: =0
    BorderThickness: =0

# HEADER LOGO
HeaderLogo As image:
    Height: =40
    Width: =40
    X: =20
    Y: =10
    Image: ="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ3aGl0ZSIgcng9IjQiLz4KPHR5cGUgeD0iMjAiIHk9IjE1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iOCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IiM0YTZmYTUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkVDPC90ZXh0Pgo8dGV4dCB4PSIyMCIgeT0iMzAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI2IiBmaWxsPSIjNGE2ZmE1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5PcmRlcjwvdGV4dD4KPC9zdmc+"

# HEADER TITLE
HeaderTitle As label:
    Text: ="EuroCaps Ordering System"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =16
    Color: =RGBA(255, 255, 255, 1)
    Height: =40
    Width: =300
    X: =70
    Y: =10

# BACK BUTTON
BackButton As button:
    Text: ="← Back"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(0, 0, 0, 0)  // Transparent
    BorderColor: =RGBA(255, 255, 255, 1)
    BorderThickness: =1
    BorderRadius: =4
    Height: =40
    Width: =100
    X: =Parent.Width - 120
    Y: =10
    OnSelect: =Navigate(Login_Schreen, ScreenTransition.Fade)

# PAGE TITLE
PageTitle As label:
    Text: ="CREATE NEW ACCOUNT"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =24
    Color: =RGBA(51, 51, 51, 1)
    Height: =40
    Width: =400
    X: =(Parent.Width - Self.Width) / 2
    Y: =80
    Align: =Align.Center

# FORM CONTAINER
FormContainer As rectangle:
    Fill: =RGBA(255, 255, 255, 1)
    Height: =800
    Width: =600
    X: =(Parent.Width - Self.Width) / 2
    Y: =140
    BorderRadius: =8
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1

# ACCOUNT INFORMATION SECTION
AccountInfoTitle As label:
    Text: ="ACCOUNT INFORMATION"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =560
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 20

# USERNAME FIELD
UsernameLabel As label:
    Text: ="Username: *"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =120
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 60

UsernameInput As text input:
    Default: =""
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =If(
        !varUsernameValid && !IsBlank(Self.Text),
        RGBA(231, 76, 60, 1),  // Red for invalid
        If(varUsernameValid, RGBA(39, 174, 96, 1), RGBA(204, 204, 204, 1))  // Green for valid, gray for default
    )
    BorderThickness: =2
    Height: =35
    Width: =300
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 55
    HintText: ="Enter username (3-20 characters)"
    OnChange: |
        =// Validate username format
        Set(varUsernameValid, 
            Len(Self.Text) >= 3 && 
            Len(Self.Text) <= 20 && 
            IsMatch(Self.Text, "^[a-zA-Z0-9_]+$")
        );
        
        // Check username availability
        Set(varUsernameAvailable, 
            IsBlank(LookUp(colUserAccounts, Username = Self.Text))
        )

UsernameError As label:
    Text: =If(
        !IsBlank(UsernameInput.Text) && !varUsernameValid,
        "Username must be 3-20 characters, alphanumeric and underscore only",
        If(!IsBlank(UsernameInput.Text) && varUsernameValid && !varUsernameAvailable,
            "Username already exists",
            ""
        )
    )
    Font: =Font.Arial
    Size: =10
    Color: =RGBA(231, 76, 60, 1)  // Red
    Height: =20
    Width: =300
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 95
    Visible: =!IsBlank(Self.Text)

# EMAIL FIELD
EmailLabel As label:
    Text: ="Email: *"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =120
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 125

EmailInput As text input:
    Default: =""
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =If(
        !varEmailValid && !IsBlank(Self.Text),
        RGBA(231, 76, 60, 1),
        If(varEmailValid, RGBA(39, 174, 96, 1), RGBA(204, 204, 204, 1))
    )
    BorderThickness: =2
    Height: =35
    Width: =300
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 120
    HintText: ="Enter email address"
    OnChange: |
        =// Validate email format
        Set(varEmailValid, IsMatch(Self.Text, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"));
        
        // Check email availability
        Set(varEmailAvailable, 
            IsBlank(LookUp(colUserAccounts, Email = Self.Text))
        )

EmailError As label:
    Text: =If(
        !IsBlank(EmailInput.Text) && !varEmailValid,
        "Please enter a valid email address",
        If(!IsBlank(EmailInput.Text) && varEmailValid && !varEmailAvailable,
            "Email address already registered",
            ""
        )
    )
    Font: =Font.Arial
    Size: =10
    Color: =RGBA(231, 76, 60, 1)
    Height: =20
    Width: =300
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 160
    Visible: =!IsBlank(Self.Text)

# PASSWORD FIELD
PasswordLabel As label:
    Text: ="Password: *"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =120
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 190

PasswordInput As text input:
    Default: =""
    Mode: =TextMode.Password
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =If(
        !varPasswordValid && !IsBlank(Self.Text),
        RGBA(231, 76, 60, 1),
        If(varPasswordValid, RGBA(39, 174, 96, 1), RGBA(204, 204, 204, 1))
    )
    BorderThickness: =2
    Height: =35
    Width: =300
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 185
    HintText: ="Enter password (min 8 characters)"
    OnChange: |
        =// Validate password strength
        Set(varPasswordValid, 
            Len(Self.Text) >= 8 && 
            IsMatch(Self.Text, "[A-Z]") &&  // Uppercase
            IsMatch(Self.Text, "[a-z]") &&  // Lowercase
            IsMatch(Self.Text, "[0-9]") &&  // Number
            IsMatch(Self.Text, "[!@#$%^&*(),.?\":{}|<>]")  // Special character
        );
        
        // Check password match
        Set(varPasswordMatch, Self.Text = ConfirmPasswordInput.Text)

PasswordError As label:
    Text: =If(
        !IsBlank(PasswordInput.Text) && !varPasswordValid,
        "Password must be 8+ chars with uppercase, lowercase, number and special character",
        ""
    )
    Font: =Font.Arial
    Size: =10
    Color: =RGBA(231, 76, 60, 1)
    Height: =40
    Width: =300
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 225
    Visible: =!IsBlank(Self.Text)

# CONFIRM PASSWORD FIELD
ConfirmPasswordLabel As label:
    Text: ="Confirm Password: *"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =120
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 275

ConfirmPasswordInput As text input:
    Default: =""
    Mode: =TextMode.Password
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =If(
        !varPasswordMatch && !IsBlank(Self.Text),
        RGBA(231, 76, 60, 1),
        If(varPasswordMatch && !IsBlank(Self.Text), RGBA(39, 174, 96, 1), RGBA(204, 204, 204, 1))
    )
    BorderThickness: =2
    Height: =35
    Width: =300
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 270
    HintText: ="Confirm your password"
    OnChange: =Set(varPasswordMatch, Self.Text = PasswordInput.Text)

ConfirmPasswordError As label:
    Text: =If(!IsBlank(ConfirmPasswordInput.Text) && !varPasswordMatch, "Passwords do not match", "")
    Font: =Font.Arial
    Size: =10
    Color: =RGBA(231, 76, 60, 1)
    Height: =20
    Width: =300
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 310
    Visible: =!IsBlank(Self.Text)

# PERSONAL INFORMATION SECTION
PersonalInfoTitle As label:
    Text: ="PERSONAL INFORMATION"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =560
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 340

# FIRST NAME FIELD
FirstNameLabel As label:
    Text: ="First Name: *"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =120
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 380

FirstNameInput As text input:
    Default: =""
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    Height: =35
    Width: =200
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 375
    HintText: ="Enter first name"

# LAST NAME FIELD
LastNameLabel As label:
    Text: ="Last Name: *"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =120
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 420

LastNameInput As text input:
    Default: =""
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    Height: =35
    Width: =200
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 415
    HintText: ="Enter last name"

# PHONE NUMBER FIELD
PhoneLabel As label:
    Text: ="Phone Number:"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =120
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 460

PhoneInput As text input:
    Default: =""
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    Height: =35
    Width: =200
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 455
    HintText: ="+31 XX XXX XXXX"

# COMPANY INFORMATION SECTION
CompanyInfoTitle As label:
    Text: ="COMPANY INFORMATION"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =560
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 500

# COMPANY NAME FIELD
CompanyNameLabel As label:
    Text: ="Company Name: *"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =120
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 540

CompanyNameInput As text input:
    Default: =""
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    Height: =35
    Width: =300
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 535
    HintText: ="Enter company name"

# COMPANY TYPE DROPDOWN
CompanyTypeLabel As label:
    Text: ="Company Type:"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =120
    X: =FormContainer.X + 20
    Y: =FormContainer.Y + 580

CompanyTypeDropdown As dropdown:
    Items: =colCompanyTypes
    Default: =First(colCompanyTypes)
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    Height: =35
    Width: =200
    X: =FormContainer.X + 150
    Y: =FormContainer.Y + 575

# CREATE ACCOUNT BUTTON
CreateAccountButton As button:
    Text: ="CREATE ACCOUNT"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(243, 156, 18, 1)  // Orange #F39C12
    HoverFill: =RGBA(230, 140, 5, 1)
    BorderColor: =RGBA(243, 156, 18, 1)
    BorderThickness: =0
    BorderRadius: =4
    Height: =45
    Width: =200
    X: =FormContainer.X + 50
    Y: =FormContainer.Y + 720
    OnSelect: |
        =// Validate all required fields
        If(
            IsBlank(UsernameInput.Text) ||
            IsBlank(EmailInput.Text) ||
            IsBlank(PasswordInput.Text) ||
            IsBlank(ConfirmPasswordInput.Text) ||
            IsBlank(FirstNameInput.Text) ||
            IsBlank(LastNameInput.Text) ||
            IsBlank(CompanyNameInput.Text),
            Notify("Please fill in all required fields", NotificationType.Error),

            // Check all validations
            If(
                !varUsernameValid || !varUsernameAvailable ||
                !varEmailValid || !varEmailAvailable ||
                !varPasswordValid || !varPasswordMatch,
                Notify("Please correct the errors in the form", NotificationType.Error),

                // Create new user account
                Patch(colUserAccounts, Defaults(colUserAccounts), {
                    UserID: Max(colUserAccounts, UserID) + 1,
                    Username: UsernameInput.Text,
                    Email: EmailInput.Text,
                    FirstName: FirstNameInput.Text,
                    LastName: LastNameInput.Text,
                    PhoneNumber: PhoneInput.Text,
                    CompanyName: CompanyNameInput.Text,
                    CompanyType: CompanyTypeDropdown.Selected.Value,
                    AccountStatus: "Active",
                    CreatedDate: Today(),
                    EmailVerified: false,
                    UserRole: "Customer"
                });

                // Show success message and navigate to login
                Notify("Account created successfully! Please log in with your new credentials.", NotificationType.Success);
                Navigate(Login_Schreen, ScreenTransition.Fade, {NewUsername: UsernameInput.Text})
            )
        )

# CLEAR FORM BUTTON
ClearFormButton As button:
    Text: ="CLEAR FORM"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(149, 165, 166, 1)  // Gray
    HoverFill: =RGBA(127, 140, 141, 1)
    BorderColor: =RGBA(149, 165, 166, 1)
    BorderThickness: =0
    BorderRadius: =4
    Height: =45
    Width: =150
    X: =FormContainer.X + 270
    Y: =FormContainer.Y + 720
    OnSelect: |
        =// Reset all form fields
        Reset(UsernameInput);
        Reset(EmailInput);
        Reset(PasswordInput);
        Reset(ConfirmPasswordInput);
        Reset(FirstNameInput);
        Reset(LastNameInput);
        Reset(PhoneInput);
        Reset(CompanyNameInput);
        Reset(CompanyTypeDropdown);

        // Reset validation variables
        Set(varUsernameValid, false);
        Set(varEmailValid, false);
        Set(varPasswordValid, false);
        Set(varPasswordMatch, false);
        Set(varUsernameAvailable, true);
        Set(varEmailAvailable, true)

# SIGN IN LINK
SignInLink As button:
    Text: ="Already have an account? Sign In"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(52, 152, 219, 1)  // Blue
    Fill: =RGBA(0, 0, 0, 0)  // Transparent
    BorderColor: =RGBA(0, 0, 0, 0)  // No border
    Height: =30
    Width: =300
    X: =(Parent.Width - Self.Width) / 2
    Y: =960
    OnSelect: =Navigate(Login_Schreen, ScreenTransition.Fade)
