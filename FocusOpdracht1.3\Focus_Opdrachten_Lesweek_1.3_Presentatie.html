<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Focus Opdrachten Lesweek 1.3 - Je Applicatielandschap in Kaart Brengen</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .presentation-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .slide {
            min-height: 100vh;
            padding: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            page-break-after: always;
            border-bottom: 3px solid #667eea;
        }
        
        .slide h1 {
            font-size: 3em;
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .slide h2 {
            font-size: 2.5em;
            color: #764ba2;
            margin-bottom: 25px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .slide h3 {
            font-size: 2em;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .slide p, .slide li {
            font-size: 1.4em;
            line-height: 1.6;
            margin-bottom: 15px;
            text-align: justify;
        }
        
        .slide ul {
            padding-left: 30px;
        }
        
        .slide li {
            margin-bottom: 10px;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea20, #764ba220);
            border-left: 5px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: start;
        }
        
        .center-content {
            text-align: center;
        }
        
        .slide-number {
            position: absolute;
            bottom: 20px;
            right: 30px;
            font-size: 1.2em;
            color: #667eea;
            font-weight: bold;
        }
        
        @media print {
            .slide {
                page-break-after: always;
                min-height: 90vh;
            }
        }
        
        .diagram-placeholder {
            background: #f8f9fa;
            border: 2px dashed #667eea;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            border-radius: 10px;
            font-style: italic;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        
        <!-- Slide 1: Titel -->
        <div class="slide center-content">
            <h1>Focus Opdrachten Lesweek 1.3</h1>
            <h2>Je Applicatielandschap in Kaart Brengen</h2>
            <div class="highlight-box">
                <p><strong>Doel:</strong> Inzicht krijgen in de huidige IT-infrastructuur en applicaties binnen een organisatie</p>
            </div>
            <div class="slide-number">1</div>
        </div>

        <!-- Slide 2: Wat is een Applicatielandschap? -->
        <div class="slide">
            <h2>Wat is een Applicatielandschap?</h2>
            <ul>
                <li><strong>Definitie:</strong> Een overzicht van alle applicaties, systemen en hun onderlinge verbindingen binnen een organisatie</li>
                <li><strong>Componenten:</strong>
                    <ul>
                        <li>Bedrijfsapplicaties</li>
                        <li>Databases</li>
                        <li>Interfaces en koppelingen</li>
                        <li>Infrastructuur</li>
                    </ul>
                </li>
                <li><strong>Doel:</strong> Transparantie en overzicht creëren voor betere besluitvorming</li>
            </ul>
            <div class="slide-number">2</div>
        </div>

        <!-- Slide 3: Waarom in Kaart Brengen? -->
        <div class="slide">
            <h2>Waarom Applicatielandschap in Kaart Brengen?</h2>
            <div class="two-column">
                <div>
                    <h3>Voordelen:</h3>
                    <ul>
                        <li>Overzicht en transparantie</li>
                        <li>Identificatie van redundanties</li>
                        <li>Risico-analyse</li>
                        <li>Optimalisatie mogelijkheden</li>
                        <li>Betere planning van wijzigingen</li>
                    </ul>
                </div>
                <div>
                    <h3>Uitdagingen:</h3>
                    <ul>
                        <li>Complexiteit van systemen</li>
                        <li>Verouderde documentatie</li>
                        <li>Verschillende stakeholders</li>
                        <li>Dynamische omgeving</li>
                    </ul>
                </div>
            </div>
            <div class="slide-number">3</div>
        </div>

        <!-- Slide 4: Stappen voor Inventarisatie -->
        <div class="slide">
            <h2>Stappen voor Inventarisatie</h2>
            <div class="highlight-box">
                <h3>1. Voorbereiding</h3>
                <ul>
                    <li>Definieer scope en doelstellingen</li>
                    <li>Identificeer stakeholders</li>
                    <li>Verzamel bestaande documentatie</li>
                </ul>
            </div>
            <div class="highlight-box">
                <h3>2. Data Verzameling</h3>
                <ul>
                    <li>Interviews met key users</li>
                    <li>Technische documentatie</li>
                    <li>Automatische discovery tools</li>
                </ul>
            </div>
            <div class="slide-number">4</div>
        </div>

        <!-- Slide 5: Inventarisatie Methoden -->
        <div class="slide">
            <h2>Inventarisatie Methoden</h2>
            <div class="two-column">
                <div>
                    <h3>Top-Down Benadering:</h3>
                    <ul>
                        <li>Start bij bedrijfsprocessen</li>
                        <li>Identificeer ondersteunende applicaties</li>
                        <li>Werk naar technische details</li>
                    </ul>
                    
                    <h3>Bottom-Up Benadering:</h3>
                    <ul>
                        <li>Start bij technische infrastructuur</li>
                        <li>Identificeer applicaties</li>
                        <li>Koppel aan bedrijfsprocessen</li>
                    </ul>
                </div>
                <div>
                    <h3>Tools en Technieken:</h3>
                    <ul>
                        <li>Network scanning</li>
                        <li>Application discovery</li>
                        <li>Database queries</li>
                        <li>Log file analyse</li>
                        <li>Interviews en workshops</li>
                    </ul>
                </div>
            </div>
            <div class="slide-number">5</div>
        </div>

        <!-- Slide 6: Visualisatie Technieken -->
        <div class="slide">
            <h2>Visualisatie Technieken</h2>
            <h3>Verschillende Diagramtypen:</h3>
            <ul>
                <li><strong>Applicatie Portfolio Diagram:</strong> Overzicht van alle applicaties</li>
                <li><strong>Integratie Diagram:</strong> Koppelingen tussen systemen</li>
                <li><strong>Layered Architecture Diagram:</strong> Technische lagen</li>
                <li><strong>Business Capability Map:</strong> Koppeling aan bedrijfsfuncties</li>
            </ul>
            
            <div class="diagram-placeholder">
                [Hier zou een voorbeeld diagram komen van een applicatielandschap]
            </div>
            <div class="slide-number">6</div>
        </div>

        <!-- Slide 7: Analyse en Evaluatie -->
        <div class="slide">
            <h2>Analyse en Evaluatie</h2>
            <h3>Analysecriteria:</h3>
            <div class="two-column">
                <div>
                    <ul>
                        <li><strong>Functionaliteit:</strong> Wat doet de applicatie?</li>
                        <li><strong>Technische staat:</strong> Hoe oud/stabiel?</li>
                        <li><strong>Bedrijfswaarde:</strong> Hoe kritiek?</li>
                        <li><strong>Kosten:</strong> TCO analyse</li>
                    </ul>
                </div>
                <div>
                    <ul>
                        <li><strong>Risico's:</strong> Security, compliance</li>
                        <li><strong>Integratie:</strong> Hoe gekoppeld?</li>
                        <li><strong>Onderhoud:</strong> Wie beheert?</li>
                        <li><strong>Toekomst:</strong> Roadmap planning</li>
                    </ul>
                </div>
            </div>
            <div class="slide-number">7</div>
        </div>

        <!-- Slide 8: Praktische Opdracht -->
        <div class="slide">
            <h2>Praktische Opdracht</h2>
            <div class="highlight-box">
                <h3>Opdracht: Inventariseer een Applicatielandschap</h3>
                <p><strong>Stappen:</strong></p>
                <ol>
                    <li>Kies een organisatie of afdeling</li>
                    <li>Identificeer alle applicaties en systemen</li>
                    <li>Documenteer koppelingen en interfaces</li>
                    <li>Maak een visueel overzicht</li>
                    <li>Analyseer en evalueer de huidige situatie</li>
                    <li>Formuleer aanbevelingen</li>
                </ol>
            </div>
            <div class="slide-number">8</div>
        </div>

        <!-- Slide 9: Tools en Software -->
        <div class="slide">
            <h2>Tools en Software</h2>
            <div class="two-column">
                <div>
                    <h3>Modeling Tools:</h3>
                    <ul>
                        <li>ArchiMate (BiZZdesign, Archi)</li>
                        <li>Visio</li>
                        <li>Lucidchart</li>
                        <li>Draw.io</li>
                    </ul>
                    
                    <h3>Discovery Tools:</h3>
                    <ul>
                        <li>Lansweeper</li>
                        <li>Device42</li>
                        <li>ServiceNow Discovery</li>
                    </ul>
                </div>
                <div>
                    <h3>Enterprise Architecture:</h3>
                    <ul>
                        <li>TOGAF framework</li>
                        <li>Zachman framework</li>
                        <li>SABSA</li>
                    </ul>
                    
                    <h3>Documentation:</h3>
                    <ul>
                        <li>Confluence</li>
                        <li>SharePoint</li>
                        <li>Notion</li>
                    </ul>
                </div>
            </div>
            <div class="slide-number">9</div>
        </div>

        <!-- Slide 10: Best Practices -->
        <div class="slide">
            <h2>Best Practices</h2>
            <ul>
                <li><strong>Start klein:</strong> Begin met een beperkte scope</li>
                <li><strong>Betrek stakeholders:</strong> Zorg voor buy-in van gebruikers</li>
                <li><strong>Houd het actueel:</strong> Regelmatige updates zijn essentieel</li>
                <li><strong>Gebruik standaarden:</strong> ArchiMate, TOGAF</li>
                <li><strong>Automatiseer waar mogelijk:</strong> Discovery tools</li>
                <li><strong>Focus op waarde:</strong> Niet alles hoeft gedetailleerd</li>
            </ul>
            
            <div class="highlight-box">
                <p><strong>Tip:</strong> Een applicatielandschap is nooit 'af' - het is een levend document dat continue onderhoud vereist.</p>
            </div>
            <div class="slide-number">10</div>
        </div>

        <!-- Slide 11: Conclusie -->
        <div class="slide center-content">
            <h2>Conclusie</h2>
            <div class="highlight-box">
                <h3>Key Takeaways:</h3>
                <ul style="text-align: left;">
                    <li>Applicatielandschap mapping is essentieel voor IT governance</li>
                    <li>Verschillende methoden en tools beschikbaar</li>
                    <li>Balans tussen detail en overzicht is belangrijk</li>
                    <li>Continue onderhoud en updates noodzakelijk</li>
                    <li>Stakeholder betrokkenheid is cruciaal voor succes</li>
                </ul>
            </div>
            <p><strong>Volgende stappen:</strong> Start met de praktische opdracht!</p>
            <div class="slide-number">11</div>
        </div>

    </div>

    <script>
        // Print functionaliteit
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                window.print();
            }
        });
        
        // Slide navigatie met pijltjestoetsen
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' && currentSlide < slides.length - 1) {
                currentSlide++;
                slides[currentSlide].scrollIntoView({ behavior: 'smooth' });
            } else if (e.key === 'ArrowLeft' && currentSlide > 0) {
                currentSlide--;
                slides[currentSlide].scrollIntoView({ behavior: 'smooth' });
            }
        });
    </script>
</body>
</html>
