import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.patches import Rectangle, Circle, FancyBboxPatch
import seaborn as sns
from matplotlib.patches import Polygon
import matplotlib.patches as mpatches

# Set style
plt.style.use('default')
sns.set_palette("husl")

def create_boonstra_strategies():
    """Visual van Boonstra's veranderstrategieën"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    strategies = ['Ontwikkelingsstrategie', 'Ingrijpende strategie', 'Machtsstrategie', 
                  'Onderhandelingsstrategie', 'Verleidingsstrategie']
    
    characteristics = {
        'Urgentie': [2, 5, 4, 3, 3],
        'Participatie': [5, 1, 2, 4, 4],
        'Snelheid': [2, 5, 4, 3, 3],
        'Duurzaamheid': [5, 2, 3, 4, 3],
        'Weerstand': [2, 4, 3, 2, 2]
    }
    
    x = np.arange(len(strategies))
    width = 0.15
    
    for i, (char, values) in enumerate(characteristics.items()):
        ax.bar(x + i*width, values, width, label=char, alpha=0.8)
    
    ax.set_xlabel('Veranderstrategieën')
    ax.set_ylabel('Score (1-5)')
    ax.set_title('Boonstra\'s Veranderstrategieën - Karakteristieken', fontsize=14, fontweight='bold')
    ax.set_xticks(x + width * 2)
    ax.set_xticklabels(strategies, rotation=45, ha='right')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Visual_1_Boonstra_Veranderstrategieen.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_caluwe_colors():
    """Visual van De Caluwé's kleurenmodel"""
    fig, ax = plt.subplots(figsize=(14, 10))
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    color_names = ['Blauw', 'Geel', 'Rood', 'Groen', 'Wit']
    characteristics = [
        'Planmatig & Rationeel',
        'Macht & Invloed', 
        'Motivatie & Relaties',
        'Leren & Ontwikkelen',
        'Emergentie & Co-creatie'
    ]
    
    # Create radar chart
    angles = np.linspace(0, 2 * np.pi, len(color_names), endpoint=False).tolist()
    angles += angles[:1]  # Complete the circle
    
    # Sample data for each color (1-5 scale)
    data = {
        'Blauw': [4, 2, 2, 1, 5],  # High on planning, low on learning
        'Geel': [2, 5, 3, 2, 3],   # High on power, medium on others
        'Rood': [2, 3, 5, 3, 2],   # High on motivation, medium on others
        'Groen': [2, 2, 3, 5, 4],  # High on learning, medium on others
        'Wit': [1, 2, 3, 4, 5]     # High on emergence, low on planning
    }
    
    for i, (color_name, values) in enumerate(data.items()):
        values += values[:1]  # Complete the circle
        ax.plot(angles, values, 'o-', linewidth=2, label=color_name, color=colors[i])
        ax.fill(angles, values, alpha=0.25, color=colors[i])
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(characteristics)
    ax.set_ylim(0, 5)
    ax.set_title('De Caluwé\'s Kleurenmodel - Denklogica\'s', fontsize=14, fontweight='bold')
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax.grid(True)
    
    plt.tight_layout()
    plt.savefig('Visual_2_Caluwe_Kleurenmodel.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_gap_analysis():
    """Visual van Gap-analyse model"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Create the gap analysis diagram
    current_state = Rectangle((1, 2), 2, 3, facecolor='lightcoral', edgecolor='red', linewidth=2)
    desired_state = Rectangle((6, 2), 2, 3, facecolor='lightgreen', edgecolor='green', linewidth=2)
    gap = Rectangle((4, 1.5), 1, 4, facecolor='lightyellow', edgecolor='orange', linewidth=2)
    
    ax.add_patch(current_state)
    ax.add_patch(desired_state)
    ax.add_patch(gap)
    
    # Add arrows
    ax.arrow(3.5, 3.5, 0.5, 0, head_width=0.2, head_length=0.1, fc='blue', ec='blue', linewidth=2)
    ax.arrow(5, 3.5, 0.5, 0, head_width=0.2, head_length=0.1, fc='blue', ec='blue', linewidth=2)
    
    # Add labels
    ax.text(2, 3.5, 'HUIDIGE\nSITUATIE\n(IST)', ha='center', va='center', fontsize=12, fontweight='bold')
    ax.text(7, 3.5, 'GEWENSTE\nSITUATIE\n(SOLL)', ha='center', va='center', fontsize=12, fontweight='bold')
    ax.text(4.5, 3.5, 'GAP\n(Acties\nNodig)', ha='center', va='center', fontsize=10, fontweight='bold')
    
    # Add action items
    actions = ['Training', 'Communicatie', 'Structuur', 'Cultuur']
    for i, action in enumerate(actions):
        ax.text(4.5, 1 + i*0.3, f'• {action}', ha='center', va='center', fontsize=9)
    
    ax.set_xlim(0, 9)
    ax.set_ylim(0, 6)
    ax.set_title('Gap-Analyse Model', fontsize=14, fontweight='bold')
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('Visual_3_Gap_Analyse_Model.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_hofstede_dimensions():
    """Visual van Hofstede's cultuurdimensies voor Euro Caps"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    dimensions = ['Machtsafstand', 'Individualisme', 'Masculiniteit', 'Onzekerheidsvermijding', 'Lange termijn', 'Toegeeflijkheid']
    euro_caps_scores = [2, 3, 4, 4, 3, 4]  # Hypothetical scores for Euro Caps
    
    bars = ax.bar(dimensions, euro_caps_scores, color='skyblue', alpha=0.7, edgecolor='navy', linewidth=1)
    
    # Add value labels on bars
    for bar, score in zip(bars, euro_caps_scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{score}', ha='center', va='bottom', fontweight='bold')
    
    ax.set_ylabel('Score (1-5)', fontsize=12)
    ax.set_title('Hofstede\'s Cultuurdimensies - Euro Caps', fontsize=14, fontweight='bold')
    ax.set_ylim(0, 5)
    ax.grid(True, alpha=0.3, axis='y')
    
    # Add interpretation
    ax.text(0.02, 0.98, 'Interpretatie:\n• Laag = 1-2\n• Gemiddeld = 3\n• Hoog = 4-5', 
            transform=ax.transAxes, fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig('Visual_4_Hofstede_Cultuurdimensies.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_kotter_8_steps():
    """Visual van Kotter's 8-stappenmodel"""
    fig, ax = plt.subplots(figsize=(16, 10))
    
    steps = [
        '1. Creëer urgentie',
        '2. Vorm leidende coalitie', 
        '3. Ontwikkel visie & strategie',
        '4. Communiceer visie',
        '5. Creëer draagvlak',
        '6. Genereer korte-termijn successen',
        '7. Consolideer verbeteringen',
        '8. Veranker in cultuur'
    ]
    
    # Create a flow diagram
    y_positions = np.linspace(8, 1, 8)
    colors = plt.cm.viridis(np.linspace(0, 1, 8))
    
    for i, (step, y, color) in enumerate(zip(steps, y_positions, colors)):
        # Create rounded rectangle for each step
        rect = FancyBboxPatch((1, y-0.3), 4, 0.6, 
                             boxstyle="round,pad=0.1", 
                             facecolor=color, edgecolor='black', linewidth=1)
        ax.add_patch(rect)
        ax.text(3, y, step, ha='center', va='center', fontsize=10, fontweight='bold')
        
        # Add arrow to next step (except for last)
        if i < len(steps) - 1:
            ax.arrow(3, y-0.4, 0, -0.2, head_width=0.2, head_length=0.1, 
                    fc='black', ec='black', linewidth=2)
    
    # Add phase labels
    ax.text(6, 7.5, 'Fase 1:\nVoorbereiding', fontsize=12, fontweight='bold',
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.7))
    ax.text(6, 5.5, 'Fase 2:\nPilot', fontsize=12, fontweight='bold',
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.7))
    ax.text(6, 3.5, 'Fase 3:\nUitrol', fontsize=12, fontweight='bold',
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.7))
    
    ax.set_xlim(0, 8)
    ax.set_ylim(0, 9)
    ax.set_title('Kotter\'s 8-Stappenmodel voor Verandering', fontsize=16, fontweight='bold')
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('Visual_5_Kotter_8_Stappenmodel.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_stakeholder_matrix():
    """Visual van Stakeholderanalyse - Power/Interest Matrix"""
    fig, ax = plt.subplots(figsize=(12, 10))
    
    # Define stakeholders with their power and interest levels
    stakeholders = {
        'CEO (Nils Clement)': (5, 5),
        'Projectleider (Niene Tepe)': (5, 5),
        'Manager Bedrijfsvoering (Servé Bosland)': (4, 5),
        'Manager ICT (Erik Dekker)': (4, 4),
        'Hoofd Financiën (Berkan Arrindell)': (4, 4),
        'Productiemanagers (Maik Ritter, Maria Stanić)': (3, 4),
        'Manager Logistiek (Rijk Wegen)': (3, 3),
        'Manager Inkoop (Ko Jager)': (3, 3),
        'HR Manager (Uwe Regel)': (2, 3),
        'Hoofd Kwaliteitsbeheer (Kees Keurig)': (3, 3),
        'Productiemedewerkers (Ismail Berenschot, Samantha Mukhlis Aswad)': (1, 3),
        'Medewerker Logistiek (Tila Karren)': (1, 2)
    }
    
    # Create scatter plot
    for stakeholder, (power, interest) in stakeholders.items():
        if 'CEO' in stakeholder or 'Projectleider' in stakeholder:
            color = 'red'
            size = 200
        elif 'Manager' in stakeholder or 'Hoofd' in stakeholder:
            color = 'orange'
            size = 150
        else:
            color = 'blue'
            size = 100
        
        ax.scatter(power, interest, s=size, c=color, alpha=0.7, edgecolors='black', linewidth=1)
        ax.annotate(stakeholder.split('(')[0].strip(), (power, interest), 
                   xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    # Add quadrant lines
    ax.axhline(y=2.5, color='gray', linestyle='--', alpha=0.5)
    ax.axvline(x=2.5, color='gray', linestyle='--', alpha=0.5)
    
    # Add quadrant labels
    ax.text(1, 4.5, 'Hoge Macht\nLaag Belang\n(Informeren)', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.7))
    ax.text(4, 4.5, 'Hoge Macht\nHoog Belang\n(Actief Betrekken)', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.7))
    ax.text(1, 1.5, 'Laag Macht\nLaag Belang\n(Minimaal Betrekken)', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.7))
    ax.text(4, 1.5, 'Laag Macht\nHoog Belang\n(Informeren)', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
    
    ax.set_xlabel('Macht/Invloed (1-5)', fontsize=12)
    ax.set_ylabel('Belang (1-5)', fontsize=12)
    ax.set_title('Stakeholderanalyse - Power/Interest Matrix', fontsize=14, fontweight='bold')
    ax.set_xlim(0, 6)
    ax.set_ylim(0, 6)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Visual_6_Stakeholderanalyse_Matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_kubler_ross_curve():
    """Visual van de Verandercurve van Kübler-Ross"""
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Define the curve data
    x = np.linspace(0, 10, 100)
    y = 3 + 2*np.sin(x - 2) + 0.5*np.sin(2*x - 4)  # Create a curve with ups and downs
    
    # Plot the curve
    ax.plot(x, y, 'b-', linewidth=3, label='Emotionele staat')
    
    # Add phase markers
    phases = [
        (1, 'Ontkenning', 'red'),
        (2.5, 'Frustratie', 'orange'),
        (4, 'Onderhandeling', 'yellow'),
        (6, 'Depressie', 'purple'),
        (8, 'Acceptatie', 'green')
    ]
    
    for x_pos, phase, color in phases:
        y_pos = 3 + 2*np.sin(x_pos - 2) + 0.5*np.sin(2*x_pos - 4)
        ax.scatter(x_pos, y_pos, s=200, c=color, edgecolors='black', linewidth=2, zorder=5)
        ax.annotate(phase, (x_pos, y_pos), xytext=(0, 20), textcoords='offset points',
                   ha='center', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
    
    # Add emotional state labels
    ax.text(0.5, 5.5, 'Hoge\nemoties', ha='center', va='center', fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.7))
    ax.text(9.5, 1.5, 'Lage\nemoties', ha='center', va='center', fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.7))
    
    ax.set_xlabel('Tijd', fontsize=12)
    ax.set_ylabel('Emotionele Staat', fontsize=12)
    ax.set_title('Verandercurve van Kübler-Ross', fontsize=14, fontweight='bold')
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 7)
    ax.grid(True, alpha=0.3)
    
    # Add legend
    ax.text(0.02, 0.98, 'Fasen van verandering:\n• Ontkenning: Verandering afwijzen\n• Frustratie: Woede en weerstand\n• Onderhandeling: Poging tot beïnvloeding\n• Depressie: Gevoel van onmacht\n• Acceptatie: Nieuwe situatie omarmen',
            transform=ax.transAxes, fontsize=9, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('Visual_7_Kubler_Ross_Verandercurve.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_mintzberg_matrix():
    """Visual van Beslissingsmatrix Mintzberg"""
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # Data from the table
    structures = ['Ondernemende organisatie', 'Machinebureaucratie', 'Professionele organisatie',
                  'Gediversifieerde organisatie', 'Innovatieve organisatie', 'Missionaire organisatie',
                  'Politieke organisatie']
    
    scores = [19, 19, 17, 17, 18, 14, 8]
    colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 'lightpink', 'lightgray', 'lightcyan']
    
    bars = ax.bar(structures, scores, color=colors, alpha=0.7, edgecolor='black', linewidth=1)
    
    # Add value labels on bars
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{score}', ha='center', va='bottom', fontweight='bold')
    
    # Highlight the chosen structures
    ax.bar(['Ondernemende organisatie', 'Machinebureaucratie'], [19, 19], 
           color=['red', 'red'], alpha=0.3, edgecolor='red', linewidth=2)
    
    ax.set_ylabel('Score', fontsize=12)
    ax.set_title('Beslissingsmatrix Organisatiestructuur (Mintzberg)', fontsize=14, fontweight='bold')
    ax.set_ylim(0, 25)
    ax.grid(True, alpha=0.3, axis='y')
    
    # Add interpretation
    ax.text(0.02, 0.98, 'Interpretatie:\n• Machinebureaucratie: Standaardisatie & controle\n• Ondernemende organisatie: Innovatie & flexibiliteit\n• Euro Caps vertoont kenmerken van beide',
            transform=ax.transAxes, fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig('Visual_8_Beslissingsmatrix_Mintzberg.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_boonstra_decision_matrix():
    """Visual van Boonstra's Beslissingsmatrix"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    strategies = ['Ontwikkelingsstrategie', 'Interne strategie', 'Machtsstrategie', 'Onderhandelingsstrategie']
    scores = [20, 15, 13, 20]
    
    colors = ['lightgreen' if score == 20 else 'lightblue' for score in scores]
    
    bars = ax.bar(strategies, scores, color=colors, alpha=0.7, edgecolor='black', linewidth=1)
    
    # Add value labels on bars
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{score}', ha='center', va='bottom', fontweight='bold')
    
    # Highlight the chosen strategy
    ax.bar(['Ontwikkelingsstrategie'], [20], color='red', alpha=0.3, edgecolor='red', linewidth=2)
    
    ax.set_ylabel('Totale Score', fontsize=12)
    ax.set_title('Beslissingsmatrix Veranderstrategie (Boonstra)', fontsize=14, fontweight='bold')
    ax.set_ylim(0, 25)
    ax.grid(True, alpha=0.3, axis='y')
    
    # Add interpretation
    ax.text(0.02, 0.98, 'Criteria:\n• Past bij cultuur\n• Past bij structuur\n• Weinig weerstand\n• Sterke samenwerking\n• Langetermijn effect',
            transform=ax.transAxes, fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig('Visual_9_Boonstra_Beslissingsmatrix.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_dmaic_integration():
    """Visual van DMAIC integratie met Kotter"""
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # Create a combined diagram showing DMAIC phases with Kotter steps
    kotter_steps = [
        '1. Creëer urgentie\n(DEFINE)',
        '2. Vorm coalitie\n(MEASURE)',
        '3. Ontwikkel visie\n(ANALYZE)',
        '4. Communiceer visie\n(IMPROVE)',
        '5. Creëer draagvlak\n(CONTROL)',
        '6. Genereer successen',
        '7. Consolideer',
        '8. Veranker'
    ]
    
    dmaic_phases = ['DEFINE', 'MEASURE', 'ANALYZE', 'IMPROVE', 'CONTROL']
    dmaic_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    
    y_positions = np.linspace(8, 1, 8)
    
    for i, (step, y) in enumerate(zip(kotter_steps, y_positions)):
        if i < 5:  # First 5 steps correspond to DMAIC
            color = dmaic_colors[i]
            dmaic_text = f'\n{dmaic_phases[i]}'
        else:
            color = 'lightgray'
            dmaic_text = ''
        
        rect = FancyBboxPatch((1, y-0.3), 4, 0.6, 
                             boxstyle="round,pad=0.1", 
                             facecolor=color, edgecolor='black', linewidth=1)
        ax.add_patch(rect)
        ax.text(3, y, step + dmaic_text, ha='center', va='center', fontsize=9, fontweight='bold')
        
        if i < len(kotter_steps) - 1:
            ax.arrow(3, y-0.4, 0, -0.2, head_width=0.2, head_length=0.1, 
                    fc='black', ec='black', linewidth=2)
    
    # Add legend
    ax.text(6, 7, 'DMAIC Integratie:\n• DEFINE: Probleem vaststellen\n• MEASURE: Data verzamelen\n• ANALYZE: Oorzaken identificeren\n• IMPROVE: Oplossingen implementeren\n• CONTROL: Resultaten borgen',
            fontsize=10, fontweight='bold',
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.7))
    
    ax.set_xlim(0, 8)
    ax.set_ylim(0, 9)
    ax.set_title('Integratie Six Sigma DMAIC met Kotter\'s 8-Stappenmodel', fontsize=14, fontweight='bold')
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('Visual_10_DMAIC_Kotter_Integratie.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Generate all visuals"""
    print("Generating all visuals for the Euro Caps change management report...")
    
    create_boonstra_strategies()
    print("✓ Visual 1: Boonstra's Veranderstrategieën")
    
    create_caluwe_colors()
    print("✓ Visual 2: De Caluwé's Kleurenmodel")
    
    create_gap_analysis()
    print("✓ Visual 3: Gap-Analyse Model")
    
    create_hofstede_dimensions()
    print("✓ Visual 4: Hofstede's Cultuurdimensies")
    
    create_kotter_8_steps()
    print("✓ Visual 5: Kotter's 8-Stappenmodel")
    
    create_stakeholder_matrix()
    print("✓ Visual 6: Stakeholderanalyse Matrix")
    
    create_kubler_ross_curve()
    print("✓ Visual 7: Kübler-Ross Verandercurve")
    
    create_mintzberg_matrix()
    print("✓ Visual 8: Beslissingsmatrix Mintzberg")
    
    create_boonstra_decision_matrix()
    print("✓ Visual 9: Boonstra's Beslissingsmatrix")
    
    create_dmaic_integration()
    print("✓ Visual 10: DMAIC-Kotter Integratie")
    
    print("\nAll visuals have been generated successfully!")
    print("Files saved as PNG images in the current directory.")

if __name__ == "__main__":
    main() 