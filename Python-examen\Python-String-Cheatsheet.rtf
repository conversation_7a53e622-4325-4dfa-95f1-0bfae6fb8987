{\rtf1\ansi\deff0
{\fonttbl{\f0 Arial;}}
\fs18
\b Python String-methoden Cheatsheet (compact) \b0\line
Let op: voorbeelden zijn kort en gericht op hoe je de methode aanroept.\line
\line
\b Voorbeelden per stringmethode (3 regels)\b0\line
text=["hallo","wereld"]\line
capitalize=[word.capitalize() for word in text]\line
print(capitalize)\line
text=["HELLO","world"]\line
casefold=[word.casefold() for word in text]\line
print(casefold)\line
text=["hi","ok"]\line
center=[word.center(6,"-") for word in text]\line
print(center)\line
text=["banana","ananas"]\line
count=[word.count("na") for word in text]\line
print(count)\line
text=["cafe","naive"]\line
encode=[word.encode("utf-8") for word in text]\line
print(encode)\line
text=["file.py","main.txt"]\line
endswith=[word.endswith(".py") for word in text]\line
print(endswith)\line
text=["a\\tb","1\\t2"]\line
expandtabs=[word.expandtabs(4) for word in text]\line
print(expandtabs)\line
text=["banana","ananas"]\line
find=[word.find("na") for word in text]\line
print(find)\line
text=["Piet","Klaas"]\line
format=["Hallo {}".format(word) for word in text]\line
print(format)\line
text=[{"x":1,"y":2},{"x":10,"y":20}]\line
format_map=["{x}-{y}".format_map(word) for word in text]\line
print(format_map)\line
text=["banana","ananas"]\line
index=[word.index("na") for word in text]\line
print(index)\line
text=["abc123","abc!","123"]\line
isalnum=[word.isalnum() for word in text]\line
print(isalnum)\line
text=["abc","abc123"]\line
isalpha=[word.isalpha() for word in text]\line
print(isalpha)\line
text=["abc","e"]\line
isascii=[word.isascii() for word in text]\line
print(isascii)\line
text=["10","12"]\line
isdecimal=[word.isdecimal() for word in text]\line
print(isdecimal)\line
text=["10","12"]\line
isdigit=[word.isdigit() for word in text]\line
print(isdigit)\line
text=["var_1","1abc"]\line
isidentifier=[word.isidentifier() for word in text]\line
print(isidentifier)\line
text=["abc","Abc"]\line
islower=[word.islower() for word in text]\line
print(islower)\line
text=["10","12"]\line
isnumeric=[word.isnumeric() for word in text]\line
print(isnumeric)\line
text=["abc","\\n"]\line
isprintable=[word.isprintable() for word in text]\line
print(isprintable)\line
text=["   ","\t","a"]\line
isspace=[word.isspace() for word in text]\line
print(isspace)\line
text=["Hello World","Hello world"]\line
istitle=[word.istitle() for word in text]\line
print(istitle)\line
text=["ABC","AbC"]\line
isupper=[word.isupper() for word in text]\line
print(isupper)\line
text=["a","b","c"]\line
join=["-".join(text)]\line
print(join)\line
text=["hi","ok"]\line
ljust=[word.ljust(5,".") for word in text]\line
print(ljust)\line
text=["AbC","XYZ"]\line
lower=[word.lower() for word in text]\line
print(lower)\line
text=["  hi","\t ok"]\line
lstrip=[word.lstrip() for word in text]\line
print(lstrip)\line
text=["eat","bee"]\line
maketrans=[str.maketrans({"e":"3","a":"@"}) for _ in text]\line
print(maketrans[0])\line
text=["a-b-c","1-2-3"]\line
partition=[word.partition("-") for word in text]\line
print(partition)\line
text=["hello","ball"]\line
replace=[word.replace("l","x",1) for word in text]\line
print(replace)\line
text=["banana","ananas"]\line
rfind=[word.rfind("na") for word in text]\line
print(rfind)\line
text=["banana","ananas"]\line
rindex=[word.rindex("na") for word in text]\line
print(rindex)\line
text=["hi","ok"]\line
rjust=[word.rjust(5,".") for word in text]\line
print(rjust)\line
text=["a-b-c","1-2-3"]\line
rpartition=[word.rpartition("-") for word in text]\line
print(rpartition)\line
text=["a b c","1 2 3"]\line
rsplit=[word.rsplit(None,1) for word in text]\line
print(rsplit)\line
text=["hi  ","ok..."]\line
rstrip=[word.rstrip() for word in text]\line
print(rstrip)\line
text=["a,b,c","1,2,3"]\line
split=[word.split(",") for word in text]\line
print(split)\line
text=["a\\nb","x\\ny"]\line
splitlines=[word.splitlines() for word in text]\line
print(splitlines)\line
text=["hello","world"]\line
startswith=[word.startswith("he") for word in text]\line
print(startswith)\line
text=["  hi  ","..ok.."]\line
strip=[word.strip(" .") for word in text]\line
print(strip)\line
text=["AbC","xYz"]\line
swapcase=[word.swapcase() for word in text]\line
print(swapcase)\line
text=["hello world","good-day"]\line
title=[word.title() for word in text]\line
print(title)\line
text=["eat","bee"]\line
translate=[word.translate(str.maketrans({"e":"3","a":"@"})) for word in text]\line
print(translate)\line
text=["abc","xYz"]\line
upper=[word.upper() for word in text]\line
print(upper)\line
text=["7","42"]\line
zfill=[word.zfill(4) for word in text]\line
print(zfill)\line
\line
\b List-methoden (uitleg)\b0\line
append() Plakt een nieuw element aan het einde van een list.\line
extend() Voegt alle elementen van een tweede list aan het einde toe.\line
insert() Voegt een element in op een specifieke index (bijv. 0 vooraan).\line
remove() Verwijdert de eerste instantie van een element (runtime error als niet bestaat).\line
pop() Verwijdert en retourneert element op index (standaard laatste); error bij out-of-range.\line
del (statement) Verwijdert element of slice via index/slice; geen retourwaarde.\line
index() Geeft index van eerste voorkomen van element (error als niet aanwezig).\line
count() Aantal keer dat element voorkomt.\line
sort() Sorteert in-place (alfabetisch/numeriek; mixed types geven error).\line
reverse() Keert volgorde in-place om.\line

\line
\page
\b Pagina 2 – compacte uitgebreide voorbeelden\b0\line
\b upper + zfill\b0\line
text=["abc","xYz"]\line
upper=[w.upper() for w in text]\line
print(upper)\line
text=["7","42"]\line
zfill=[w.zfill(5) for w in text]\line
print(zfill)\line
\line
\b append / extend / insert\b0\line
text=[["a"],["x","y"]]\line
append=[(lambda lst: (lst.append("b"), lst)[1])(lst) for lst in text]\line
print(append)\line
text=[["a"],["x"]]\line
extend=[(lambda lst: (lst.extend(["b","c"]), lst)[1])(lst) for lst in text]\line
print(extend)\line
text=[["a","c"],["x","z"]]\line
insert=[(lambda lst: (lst.insert(1,"b"), lst)[1])(lst) for lst in text]\line
print(insert)\line
\line
\b remove / pop / del\b0\line
text=[["a","b","c"],["x","y","z"]]\line
remove=[(lambda lst: (lst.remove("b"), lst)[1])(lst) for lst in text]\line
print(remove)\line
text=[["a","b","c"],["x","y","z"]]\line
pop=[(lambda lst: (lst.pop(1), lst)[1])(lst) for lst in text]\line
print(pop)\line
text=[["a","b","c"],["x","y","z"]]\line
del_examples=[(lambda lst: ( (lambda: (lst.__setitem__(slice(0,2), [])))(), lst)[1])(lst) for lst in text]\line
print(del_examples)\line
\line
\b index / count / sort / reverse\b0\line
text=[["a","b","a"],["x","x","y"]]\line
index=[lst.index("a") for lst in text]\line
print(index)\line
count=[lst.count("a") for lst in text]\line
print(count)\line
text=[[3,1,2],["b","a","c"]]\line
sort=[(lambda lst: (lst.sort(), lst)[1])(lst) for lst in text]\line
print(sort)\line
text=[[1,2,3],["a","b","c"]]\line
reverse=[(lambda lst: (lst.reverse(), lst)[1])(lst) for lst in text]\line
print(reverse)\line

\line
\b Q&A (algemeen, examen-gericht)\b0\line
Q: s='abcdef'; s[1:5:2]  A: 'bd'\line
Q: s='abcdef'; s[-2:]    A: 'ef'\line
Q: [x**2 for x in range(3)]  A: [0,1,4]\line
Q: {x:x**2 for x in (1,2,3)} A: {1:1,2:4,3:9}\line
Q: len(set([1,1,2,3]))   A: 3\line
Q: list(enumerate(['a','b'],start=1))  A: [(1,'a'),(2,'b')]\line
Q: list(zip([1,2],[3],[9,8]))  A: [(1,3,9)]\line
Q: a,*b=[1,2,3]  A: a=1, b=[2,3]\line
Q: def f(a,b): return a+b; f(*[1,2])  A: 3\line
Q: d={'a':1}; d.get('x',0)  A: 0\line
Q: sorted(['aa','b','ccc'], key=len, reverse=True)  A: ['ccc','aa','b']\line
Q: [w for w in ['a','',None] if w]  A: ['a']\line
Q: any([])? all([])?  A: any([])=False, all([])=True\line
Q: try/except/else/finally  A: try:... except:... else:... finally:...\line
Q: with open('f.txt','w',encoding='utf-8') as f: f.write('x')  A: schrijft naar bestand\line
Q: f"{3.14159:.2f}"  A: '3.14'\line
Q: '{:>5}'.format('a')  A: '    a'\line
Q: bool(None), bool(''), bool([0])  A: False, False, True\line
Q: tuple vs list  A: tuple is onveranderlijk; list veranderlijk\line
\line
\b Print\b0\line
Deze sheet is geoptimaliseerd voor maximaal 2 pagina\'s (A4, Arial 12pt).\line
}

