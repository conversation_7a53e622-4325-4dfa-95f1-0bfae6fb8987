# PowerShell script to add more slides to the existing PowerPoint presentation

try {
    # Open existing PowerPoint presentation
    $powerpoint = New-Object -ComObject PowerPoint.Application
    $powerpoint.Visible = [Microsoft.Office.Core.MsoTriState]::msoTrue
    
    $presentationPath = "C:\Users\<USER>\Documents\augment-projects\Americaps\FocusOpdracht1.4\Logistix-BV-Presentatie.pptx"
    $presentation = $powerpoint.Presentations.Open($presentationPath)
    
    # Slide 7: Application Landscape
    $slide7 = $presentation.Slides.Add(7, 2)
    $slide7.Shapes.Title.TextFrame.TextRange.Text = "Applicatielandschap - Huidige Situatie"
    $content7 = @"
WMS (Warehouse Management System)
• Type: On-premise systeem
• Functie: Magazijnprocessen aansturen
• Probleem: Oudste applicatie, beperkte integratie, kostbare uitbreidingen

ERP (Enterprise Resource Planning)
• Type: Cloud-gebaseerd systeem
• Functie: Financiën, facturatie, klantbeheer
• Probleem: Data niet altijd accuraat door handmatige invoer

TMS (Transport Management System)
• Type: Standalone softwarepakket
• Functie: Verzendlabels, communicatie vervoerders
• Probleem: Handmatige data-uitwisseling met WMS en ERP

CRM (Customer Relationship Management)
• Type: Losstaand systeem
• Functie: Klantcontact en klachten registratie
• Probleem: Geen automatische synchronisatie van order/status info
"@
    $slide7.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content7
    
    # Slide 8: ArchiMate Architecture
    $slide8 = $presentation.Slides.Add(8, 2)
    $slide8.Shapes.Title.TextFrame.TextRange.Text = "ArchiMate Architectuur Screenshot"
    $content8 = @"
[HIER KOMT DE SCREENSHOT VAN HET ARCHIMATE DIAGRAM]

Architectuur toont:
• Business Layer: Actoren, processen en services
• Application Layer: Systemen en hun onderlinge relaties
• Technology Layer: Infrastructuur en platforms

Belangrijke observaties:
• Veel handmatige stappen (rood gemarkeerd)
• Beperkte automatische koppelingen
• Geïsoleerde systemen zonder integratie

Bestand: Logistix-BV-Architectuur.archimate
"@
    $slide8.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content8
    
    # Slide 9: Improvement Proposal 1
    $slide9 = $presentation.Slides.Add(9, 2)
    $slide9.Shapes.Title.TextFrame.TextRange.Text = "Verbetervoorstel 1 - Integratie Platform (APM)"
    $content9 = @"
Implementatie van Enterprise Service Bus (ESB)

Probleem aangepakt: Gebrek aan integratie tussen systemen

Oplossing:
• Centraal integratieplatform tussen alle systemen
• Real-time data synchronisatie
• Eliminatie van handmatige dubbele invoer

Voordelen:
• Automatische orderverwerking van ontvangst tot ERP/WMS
• Consistente data tussen alle systemen
• Verminderde fouten en verwerkingstijd

APM Classificatie: INVEST - Strategische verbetering voor toekomst
Prioriteit: Hoog - Lost meerdere kernproblemen op
"@
    $slide9.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content9
    
    # Slide 10: Improvement Proposal 2
    $slide10 = $presentation.Slides.Add(10, 2)
    $slide10.Shapes.Title.TextFrame.TextRange.Text = "Verbetervoorstel 2 - API Modernisering (APM)"
    $content10 = @"
Vervanging Verouderde API & Nieuwe Koppelingen

Probleem aangepakt: Verouderde API en handmatige processen

Oplossing:
• Moderne REST API's voor klantorders
• Directe koppeling WMS ↔ TMS voor verzendlabels
• Automatische tracking synchronisatie TMS ↔ ERP

Voordelen:
• Geautomatiseerde verzendlabel generatie
• Automatische tracking updates
• Snellere orderverwerking

APM Classificatie: MIGRATE - Vervanging verouderde technologie
Prioriteit: Hoog - Kritiek voor operationele efficiëntie
"@
    $slide10.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content10
    
    # Slide 11: Improvement Proposal 3
    $slide11 = $presentation.Slides.Add(11, 2)
    $slide11.Shapes.Title.TextFrame.TextRange.Text = "Verbetervoorstel 3 - Customer Portal & CRM Integratie (APM)"
    $content11 = @"
Geïntegreerd Klantportaal met Real-time Status

Probleem aangepakt: Inefficiënte klantenservice en handmatige status opzoek

Oplossing:
• Self-service klantportaal met real-time order tracking
• CRM integratie met ERP en TMS voor complete klanthistorie
• Geautomatiseerde status updates naar klanten

Voordelen:
• Verminderde belasting klantenservice
• Betere klantervaring door transparantie
• Snellere probleemoplossing door geïntegreerde data

APM Classificatie: ENHANCE - Verbetering klantervaring
Prioriteit: Medium - Toegevoegde waarde na basis integraties
"@
    $slide11.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content11
    
    # Slide 12: Implementation Roadmap
    $slide12 = $presentation.Slides.Add(12, 2)
    $slide12.Shapes.Title.TextFrame.TextRange.Text = "Implementatie Roadmap"
    $content12 = @"
Fase 1 (0-6 maanden): Basis Integratie
• ESB implementatie tussen ERP en WMS
• Eliminatie dubbele orderinvoer
• Quick win: Directe ROI door tijdsbesparing

Fase 2 (6-12 maanden): API Modernisering
• Nieuwe klant API's
• WMS-TMS koppeling voor verzendlabels
• TMS-ERP koppeling voor tracking

Fase 3 (12-18 maanden): Customer Experience
• CRM integratie met alle systemen
• Klantportaal ontwikkeling
• Self-service mogelijkheden

Verwachte Resultaten:
• 60% reductie handmatige taken
• 40% snellere orderverwerking
• 80% reductie klantenservice vragen over status
"@
    $slide12.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content12
    
    # Slide 13: Conclusion
    $slide13 = $presentation.Slides.Add(13, 2)
    $slide13.Shapes.Title.TextFrame.TextRange.Text = "Conclusie & Aanbevelingen"
    $content13 = @"
Huidige Situatie:
• Versnipperd IT-landschap met veel handmatige processen
• Operationele inefficiënties en verhoogd foutenrisico
• Slechte klantervaring door trage responstijden

Aanbevolen Aanpak:
1. Start met ESB implementatie - Grootste impact op operationele efficiëntie
2. Moderniseer API's gefaseerd - Vermijd big-bang implementatie
3. Investeer in klantervaring - Concurrentievoordeel op lange termijn

Kritieke Succesfactoren:
• Management commitment voor gefaseerde aanpak
• Training medewerkers op nieuwe processen
• Monitoring en continue optimalisatie

Verwachte ROI: 18-24 maanden door besparingen op personeelskosten en verhoogde klanttevredenheid
"@
    $slide13.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content13
    
    # Slide 14: Questions
    $slide14 = $presentation.Slides.Add(14, 2)
    $slide14.Shapes.Title.TextFrame.TextRange.Text = "Vragen & Discussie"
    $content14 = @"
Bedankt voor uw aandacht!

Discussiepunten:
• Prioritering van de voorgestelde verbeteringen
• Budget en resource allocatie
• Timing van implementatie fasen
• Change management aanpak

Contact:
• [Naam student]
• [Email]
• [Datum presentatie]

Bestanden:
• Logistix-BV-Architectuur.archimate
• Logistix-BV-Presentatie.pptx
"@
    $slide14.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content14
    
    # Save the presentation
    $presentation.Save()
    
    Write-Host "Additional slides added successfully to the PowerPoint presentation!"
    
} catch {
    Write-Error "Error adding slides to PowerPoint presentation: $($_.Exception.Message)"
}
