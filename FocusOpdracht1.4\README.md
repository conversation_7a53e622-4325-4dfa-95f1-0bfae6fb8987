# Focusopdracht 1.4 - EA en APM: Casus Logistix B.V.

## Overzicht
Deze map bevat alle bestanden voor de Focusopdracht 1.4 over Enterprise Architectuur (EA) en Application Portfolio Management (APM) voor de casus van Logistix B.V.

## Bestanden in deze map:

### 1. **Logistix-BV-Architectuur.archimate**
- ArchiMate architectuur model van de huidige situatie bij Logistix B.V.
- Toont Business, Application en Technology layers
- Visualiseert de knelpunten en handmatige processen
- Conform de lessen van ALM lesweek 1.3
- **GEFIXED**: Bestand was corrupt, nu gerepareerd met correcte XML structuur

### 1b. **Logistix-BV-Simple.archimate** (Backup)
- Eenvoudigere versie van het ArchiMate model
- Minder complexe structuur, gegarandeerd werkend
- Gebruik deze als de hoofdversie niet werkt

### 2. **Logistix-BV-Presentatie.pptx**
- Complete PowerPoint presentatie met 14 slides
- Bevat alle vereiste onderdelen volgens de opdracht
- <PERSON><PERSON>ar voor presentatie

### 3. **Logistix-BV-Presentatie.md**
- Markdown versie van de presentatie content
- Backup en overzicht van alle slide content

### 4. **Fietsenhok - Fietsverhuur.archimate**
- Voorbeeld ArchiMate bestand (referentie)
- Gebruikt als template voor de structuur

### 5. **Focusopdracht week 1.4 - EA en APM- Casus Logistix BV.pdf**
- Originele opdracht PDF met de casus beschrijving

## Casus Samenvatting: Logistix B.V.

### Bedrijfsprofiel
- **Sector**: Logistieke dienstverlening
- **Specialisatie**: E-fulfilment en opslag- & distributieoplossingen voor webshops
- **Probleem**: Gebrek aan synergie tussen systemen, toegenomen complexiteit

### Huidige IT-landschap
1. **WMS (Warehouse Management System)**: On-premise, oudste systeem, beperkte integratie
2. **ERP (Enterprise Resource Planning)**: Cloud-based, financiën en klantbeheer
3. **TMS (Transport Management System)**: Standalone, verzendlabels en vervoerders
4. **CRM (Customer Relationship Management)**: Losstaand, klantcontact registratie

### Belangrijkste Knelpunten
1. **Handmatige dubbele invoer**: Orders moeten in zowel ERP als WMS
2. **Gebrek aan integratie**: Systemen staan los van elkaar
3. **Verouderde technologie**: Beperkte API's en kostbare uitbreidingen
4. **Inefficiënte processen**: Handmatig kopiëren van data tussen systemen
5. **Data inconsistentie**: Door handmatige invoer niet altijd accurate data

### Bedrijfsprocessen met problemen

#### Orderverwerking:
- Grote klanten: Verouderde API-koppeling
- Kleine klanten: E-mail of batchbestanden
- Dubbele handmatige invoer in ERP én WMS
- Losgekoppelde validatie (klant vs voorraad)

#### Magazijnbeheer:
- WMS genereert picklijst
- Handmatige opzoek vervoerder info
- Handmatige invoer verzendlabels in TMS
- Handmatig kopiëren trackingnummers naar ERP

#### Klantenservice:
- CRM staat los van andere systemen
- Voor elke vraag: opzoeken in ERP + TMS + handmatig samenvoegen
- Lange responstijden

## Verbetervoorstellen (APM)

### 1. **Integratie Platform (ESB)**
- **APM Classificatie**: INVEST
- **Prioriteit**: Hoog
- **Oplossing**: Enterprise Service Bus tussen alle systemen
- **Voordeel**: Eliminatie dubbele invoer, real-time synchronisatie

### 2. **API Modernisering**
- **APM Classificatie**: MIGRATE
- **Prioriteit**: Hoog
- **Oplossing**: Moderne REST API's, directe WMS-TMS koppeling
- **Voordeel**: Geautomatiseerde verzendlabels en tracking

### 3. **Customer Portal & CRM Integratie**
- **APM Classificatie**: ENHANCE
- **Prioriteit**: Medium
- **Oplossing**: Self-service portal, geïntegreerde CRM
- **Voordeel**: Betere klantervaring, minder belasting klantenservice

## Implementatie Roadmap

### Fase 1 (0-6 maanden): Basis Integratie
- ESB implementatie tussen ERP en WMS
- Eliminatie dubbele orderinvoer
- **Quick win**: Directe ROI door tijdsbesparing

### Fase 2 (6-12 maanden): API Modernisering
- Nieuwe klant API's
- WMS-TMS koppeling voor verzendlabels
- TMS-ERP koppeling voor tracking

### Fase 3 (12-18 maanden): Customer Experience
- CRM integratie met alle systemen
- Klantportaal ontwikkeling
- Self-service mogelijkheden

## Verwachte Resultaten
- **60% reductie** handmatige taken
- **40% snellere** orderverwerking
- **80% reductie** klantenservice vragen over status
- **ROI**: 18-24 maanden

## Inlevering Checklist ✅

Volgens de opdracht moet worden ingeleverd:

✅ **PowerPoint presentatie met:**
- ✅ Korte uitleg over het bedrijf en de processen
- ✅ De belangrijkste knelpunten
- ✅ Screenshot van het ArchiMate architectuur conform ALM lesweek 1.3
- ✅ (Minimaal) 3 verbetervoorstellen met behulp van APM

## Gebruik van de bestanden

1. **Voor presentatie**: Open `Logistix-BV-Presentatie.pptx`
2. **Voor architectuur analyse**: Open `Logistix-BV-Architectuur.archimate` in ArchiMate tool
3. **Voor screenshot**: Maak screenshot van het ArchiMate diagram voor slide 8

## Opmerkingen
- Het ArchiMate bestand is gebaseerd op het voorbeeld `Fietsenhok - Fietsverhuur.archimate`
- Alle verbetervoorstellen zijn geclassificeerd volgens APM methodologie
- De roadmap is gefaseerd om risico's te minimaliseren en quick wins te realiseren
