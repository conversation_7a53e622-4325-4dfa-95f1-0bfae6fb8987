# PowerShell script to create PowerPoint presentation for Logistix B.V.

try {
    # Create PowerPoint application
    $powerpoint = New-Object -ComObject PowerPoint.Application
    $powerpoint.Visible = [Microsoft.Office.Core.MsoTriState]::msoTrue
    
    # Create new presentation
    $presentation = $powerpoint.Presentations.Add()
    
    # Slide 1: Title
    $slide1 = $presentation.Slides.Add(1, 1)
    $slide1.Shapes.Title.TextFrame.TextRange.Text = "Logistix B.V. - Enterprise Architectuur Analyse"
    $slide1.Shapes.Placeholders[2].TextFrame.TextRange.Text = "Focusopdracht 1.4 - EA en APM`nCasus: IT-landschap optimalisatie`nStudent: [Naam]"
    
    # Slide 2: Company Profile
    $slide2 = $presentation.Slides.Add(2, 2)
    $slide2.Shapes.Title.TextFrame.TextRange.Text = "Bedrijfsprofiel Logistix B.V."
    $content2 = @"
Over het bedrijf:
• Sector: Logistieke dienstverlening
• Specialisatie: E-fulfilment en opslag- & distributieoplossingen
• Doelgroep: Webshops (grote en kleine klanten)
• Probleem: Gebrek aan synergie tussen systemen
• Doel: IT-landschap optimaliseren vanwege toegenomen complexiteit

Huidige situatie:
• Verouderde systemen met beperkte integratie
• Handmatige processen leiden tot fouten en vertragingen
• Operationele problemen door gebrek aan automatisering
"@
    $slide2.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content2
    
    # Slide 3: Order Processing
    $slide3 = $presentation.Slides.Add(3, 2)
    $slide3.Shapes.Title.TextFrame.TextRange.Text = "Bedrijfsprocessen - Orderverwerking"
    $content3 = @"
Huidige orderverwerking:
1. Order ontvangst:
   • Grote klanten: Verouderde API-koppeling
   • Kleine klanten: E-mail of batchbestanden

2. Dubbele handmatige invoer:
   • Ordergegevens invoeren in ERP-systeem
   • Dezelfde gegevens opnieuw invoeren in WMS
   • Gevolg: Veel fouten en vertraging

3. Losgekoppelde validatie:
   • ERP controleert klantgegevens
   • WMS valideert voorraad
   • Handmatig schakelen tussen systemen
"@
    $slide3.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content3
    
    # Slide 4: Warehouse Management
    $slide4 = $presentation.Slides.Add(4, 2)
    $slide4.Shapes.Title.TextFrame.TextRange.Text = "Bedrijfsprocessen - Magazijnbeheer"
    $content4 = @"
Picking & Packing:
• WMS genereert picklijst
• Probleem: Handmatige opzoek van vervoerder info
• Probleem: Handmatige invoer verzendlabels in TMS
• Geen directe koppeling tussen picking en verzending

Verzending:
• Probleem: Handmatig kopiëren van trackingnummers
• Van TMS naar ERP voor facturatie
• Van TMS naar ERP voor klantcommunicatie
• Gevolg: Tijdrovend en foutgevoelig
"@
    $slide4.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content4
    
    # Slide 5: Customer Service
    $slide5 = $presentation.Slides.Add(5, 2)
    $slide5.Shapes.Title.TextFrame.TextRange.Text = "Bedrijfsprocessen - Klantenservice"
    $content5 = @"
Huidige situatie:
• CRM-systeem staat los van alle andere applicaties
• Probleem: Geen automatische synchronisatie van order/status info

Voor elke klantvraag:
1. Order opzoeken in ERP-systeem
2. Status opzoeken in TMS
3. Handmatig samenvoegen van informatie
4. Gevolg: Lange responstijd voor klanten
"@
    $slide5.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content5
    
    # Slide 6: Key Issues
    $slide6 = $presentation.Slides.Add(6, 2)
    $slide6.Shapes.Title.TextFrame.TextRange.Text = "Belangrijkste Knelpunten"
    $content6 = @"
1. Handmatige dubbele invoer
   • Orders moeten in zowel ERP als WMS ingevoerd worden
   • Verhoogd risico op fouten en inconsistenties

2. Gebrek aan integratie
   • Systemen staan los van elkaar
   • Geen automatische data-uitwisseling
   • Handmatig schakelen tussen systemen

3. Verouderde technologie
   • WMS heeft beperkte integratiemogelijkheden
   • API-koppelingen zijn verouderd
   • Kostbare uitbreidingen

4. Inefficiënte processen
   • Handmatig kopiëren van trackingnummers
   • Handmatige opzoek van verzendinfo
   • Lange responstijden klantenservice

5. Data inconsistentie
   • Door handmatige invoer niet altijd accurate data
   • ERP is bedoeld als 'bron van waarheid' maar data klopt niet altijd
"@
    $slide6.Shapes.Placeholders[2].TextFrame.TextRange.Text = $content6
    
    # Save the presentation
    $savePath = "C:\Users\<USER>\Documents\augment-projects\Americaps\FocusOpdracht1.4\Logistix-BV-Presentatie.pptx"
    $presentation.SaveAs($savePath)
    
    Write-Host "PowerPoint presentation created successfully at: $savePath"
    
} catch {
    Write-Error "Error creating PowerPoint presentation: $($_.Exception.Message)"
}
