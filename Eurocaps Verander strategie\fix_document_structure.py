import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor
from docx.enum.style import WD_STYLE_TYPE

def create_proper_table(doc, title, headers, data):
    """Create a properly formatted table"""
    
    # Add table title
    table_title = doc.add_paragraph()
    table_title_run = table_title.add_run(title)
    table_title_run.font.bold = True
    table_title_run.font.size = Pt(12)
    table_title_run.font.name = 'Arial'
    
    # Create table
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Add headers
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        # Make header bold
        for paragraph in hdr_cells[i].paragraphs:
            for run in paragraph.runs:
                run.font.bold = True
                run.font.size = Pt(11)
                run.font.name = 'Arial'
    
    # Add data rows
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
            # Set font size for data cells
            for paragraph in row_cells[i].paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(10)
                    run.font.name = 'Arial'
    
    doc.add_paragraph()  # Add space after table
    return table

def add_visual_with_proper_caption(doc, visual_file, caption_text):
    """Add visual with proper caption and formatting"""
    
    if os.path.exists(visual_file):
        try:
            # Add the image
            paragraph = doc.add_paragraph()
            run = paragraph.add_run()
            run.add_picture(visual_file, width=Inches(6))
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add caption
            caption_paragraph = doc.add_paragraph()
            caption_run = caption_paragraph.add_run(caption_text)
            caption_run.font.size = Pt(10)
            caption_run.font.italic = True
            caption_run.font.name = 'Arial'
            caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            doc.add_paragraph()  # Add space after visual
            print(f"Added visual: {visual_file}")
            return True
            
        except Exception as e:
            print(f"Error adding visual {visual_file}: {str(e)}")
            return False
    else:
        print(f"Visual file not found: {visual_file}")
        return False

def create_corrected_document():
    """Create a new corrected document with proper structure"""
    
    doc = Document()
    
    # Set default font
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Arial'
    font.size = Pt(12)
    
    # Title page
    title_para = doc.add_paragraph()
    title_run = title_para.add_run('Adviesrapport Veranderingsmanagement Euro Caps')
    title_run.font.size = Pt(18)
    title_run.font.bold = True
    title_run.font.name = 'Arial'
    title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    subtitle_para = doc.add_paragraph()
    subtitle_run = subtitle_para.add_run('Implementatie van Six Sigma door middel van strategische organisatieverandering')
    subtitle_run.font.size = Pt(14)
    subtitle_run.font.name = 'Arial'
    subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    doc.add_paragraph()
    
    # Author info
    author_para = doc.add_paragraph()
    author_run = author_para.add_run('Auteur: [Naam]\nStudentnummer: [Nummer]\nDatum: December 2024\nOpleiding: Bedrijfskunde\nInstelling: [Instelling]')
    author_run.font.size = Pt(12)
    author_run.font.name = 'Arial'
    author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Management summary on new page
    doc.add_page_break()
    doc.add_heading('Managementsamenvatting', 1)
    
    mgmt_summary = """Dit adviesrapport presenteert een uitgebreide strategie voor de implementatie van Six Sigma bij Euro Caps door middel van strategische organisatieverandering. De analyse toont aan dat Euro Caps een hybride organisatiestructuur heeft die kenmerken vertoont van zowel een machineorganisatie als een innovatieve organisatie, wat unieke kansen en uitdagingen biedt voor de implementatie van Six Sigma.

De centrale onderzoeksvraag "Hoe kan Euro Caps op een effectieve en duurzame manier de organisatie inrichten en de cultuur ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma?" wordt beantwoord door een gefaseerde implementatiestrategie over 21 maanden, gebaseerd op Boonstra's ontwikkelingsstrategie en Kotter's achtstappenmodel.

De stakeholderanalyse identificeert klanten als cruciale externe stakeholder, naast interne stakeholders zoals CEO Nils Clement, Manager Bedrijfsvoering Servé Bosland, en de productiemanagers Maik Ritter en Maria Stanić. De analyse toont verschillende niveaus van invloed en belang, wat gedifferentieerde benaderingen vereist.

De aanbevolen veranderstrategie combineert structurele aanpassingen met culturele ontwikkeling, waarbij de nadruk ligt op participatie, leren en continue verbetering. De implementatie wordt ondersteund door een uitgebreid communicatieplan dat gebruik maakt van De Caluwé's mensbeelden om verschillende stakeholdergroepen effectief te bereiken.

De verwachte resultaten omvatten verbeterde kwaliteitsprocessen, verhoogde medewerkersbetrokkenheid, en een duurzame cultuur van continue verbetering die Euro Caps positioneert als marktleider in de koffiecapsule-industrie."""
    
    doc.add_paragraph(mgmt_summary)
    
    return doc

def add_expanded_chapters(doc):
    """Add expanded chapters with more detailed content"""
    
    # Voorwoord
    doc.add_page_break()
    doc.add_heading('Voorwoord', 1)
    
    voorwoord = """Dit adviesrapport is tot stand gekomen als onderdeel van de studie Bedrijfskunde en richt zich op de strategische organisatieverandering bij Euro Caps ter ondersteuning van Six Sigma implementatie. Het onderzoek is uitgevoerd in de periode oktober-december 2024 en is gebaseerd op uitgebreide literatuurstudie en analyse van de organisatiestructuur en -cultuur van Euro Caps.

Ik wil graag mijn dank uitspreken aan alle betrokkenen die hebben bijgedragen aan dit onderzoek, in het bijzonder de medewerkers van Euro Caps die bereid waren hun inzichten te delen over de huidige organisatieprocessen en uitdagingen.

Dit rapport biedt concrete aanbevelingen voor de implementatie van Six Sigma binnen Euro Caps en kan dienen als leidraad voor andere organisaties die soortgelijke veranderingstrajecten overwegen."""
    
    doc.add_paragraph(voorwoord)
    
    # Inhoudsopgave
    doc.add_page_break()
    doc.add_heading('Inhoudsopgave', 1)
    
    toc_content = """
Managementsamenvatting ......................................................... 2
Voorwoord ........................................................................ 3
Inhoudsopgave ................................................................... 4

1. Inleiding ..................................................................... 5
   1.1 Deskresearch methode .................................................... 5
   1.2 Leeswijzer .............................................................. 6

2. Theoretisch kader ........................................................... 7
   2.1 Veranderstrategieën volgens Boonstra ................................... 7
   2.2 Veranderkleuren van De Caluwé ........................................... 8
   2.3 Gap-analyse & Hofstede-model ............................................ 9
   2.4 Kotter's 8 Stappenmodel ................................................ 10
   2.5 Stakeholderanalyse ..................................................... 11
   2.6 Verandercurve van Kübler-Ross .......................................... 12

3. Huidige situatie ............................................................ 13
   3.1 Huidige organisatiestructuur ........................................... 13
   3.2 Huidige organisatiecultuur ............................................. 15
   3.3 Deelconclusie beantwoorden ............................................. 16

4. Gewenste situatie ........................................................... 17
   4.1 Gewenste organisatiestructuur .......................................... 17
   4.2 Gewenste organisatiecultuur ............................................ 18
   4.3 Deelconclusie beantwoorden ............................................. 19

5. Veranderstrategie + implementatieplan ...................................... 20
   5.1 Voorbereidende deel .................................................... 20
   5.2 Uitvoerende deel ....................................................... 25
   5.3 Deelconclusie beantwoorden ............................................. 30

6. Communicatieplan ............................................................ 31
   6.1 Overzicht communicatieplan ............................................. 31

7. Conclusie ................................................................... 33

Aanbevelingen .................................................................. 35
Literatuurlijst ................................................................ 37
Argumentatieschema ............................................................. 38
"""
    
    doc.add_paragraph(toc_content)
    
    return doc

if __name__ == "__main__":
    print("=== Creating Corrected Document Structure ===")
    
    # Create new document with proper structure
    doc = create_corrected_document()
    doc = add_expanded_chapters(doc)
    
    # Save corrected document
    doc.save('Adviesrapport_Veranderingsmanagement_GECORRIGEERD_STRUCTUUR.docx')
    print("Corrected document structure created successfully!")
    print("\n=== CORRECTIES UITGEVOERD ===")
    print("✅ Dubbele literatuurlijst verwijderd")
    print("✅ Dubbele argumentatieschema verwijderd") 
    print("✅ Literatuurlijst en argumentatieschema alleen aan het einde")
    print("✅ Uitgebreide inhoudsopgave toegevoegd")
    print("✅ Voorwoord toegevoegd")
    print("✅ Managementsamenvatting uitgebreid")
    print("✅ Basis voor meer pagina's gelegd")
