<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.opengroup.org/xsd/archimate/3.0/" id="id-logistix-model" version="1.0.0">
  <folder name="Business" id="f-business" type="business">
    <element xsi:type="archimate:BusinessActor" name="Klant" id="b-actor-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Medewerker" id="b-actor-medewerker"/>
    <element xsi:type="archimate:BusinessService" name="Orderafhandeling" id="b-service-orderafhandeling"/>
    <element xsi:type="archimate:BusinessProcess" name="Orderverwerking" id="b-process-orderverwerking"/>
    <element xsi:type="archimate:BusinessProcess" name="Magazijnbeheer (Picking &amp; Packing)" id="b-process-magazijn"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantenservice &amp; Communicatie" id="b-process-klantenservice"/>
  </folder>
  <folder name="Application" id="f-application" type="application">
    <element xsi:type="archimate:ApplicationComponent" name="ERP (cloud)" id="a-app-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS (on-premise)" id="a-app-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS (standalone)" id="a-app-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM (losstaand)" id="a-app-crm"/>
    <element xsi:type="archimate:ApplicationComponent" name="API-koppeling (verouderd)" id="a-app-api"/>
    <element xsi:type="archimate:ApplicationComponent" name="E-mail / Batch import" id="a-app-emailbatch"/>
    <element xsi:type="archimate:ApplicationService" name="Orderbeheer Service" id="a-service-orderbeheer"/>
    <element xsi:type="archimate:ApplicationService" name="Voorraad Service" id="a-service-voorraad"/>
    <element xsi:type="archimate:ApplicationService" name="Verzendlabel &amp; Tracking Service" id="a-service-verzending"/>
  </folder>
  <folder name="Technology" id="f-technology" type="technology">
    <element xsi:type="archimate:Node" name="On-prem WMS Server" id="t-node-wms"/>
    <element xsi:type="archimate:Node" name="Cloud Platform (ERP)" id="t-node-cloud"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Netwerk / Internet" id="t-network"/>
  </folder>
  <folder name="Relations" id="f-relations" type="relations">
    <!-- Business to Application -->
    <element xsi:type="archimate:UsedByRelationship" id="r-1" source="b-process-orderverwerking" target="a-service-orderbeheer"/>
    <element xsi:type="archimate:UsedByRelationship" id="r-2" source="b-process-magazijn" target="a-service-voorraad"/>
    <element xsi:type="archimate:UsedByRelationship" id="r-3" source="b-process-klantenservice" target="a-app-crm"/>
    <!-- Application structural -->
    <element xsi:type="archimate:RealizationRelationship" id="r-4" source="a-service-orderbeheer" target="a-app-erp"/>
    <element xsi:type="archimate:RealizationRelationship" id="r-5" source="a-service-voorraad" target="a-app-wms"/>
    <element xsi:type="archimate:RealizationRelationship" id="r-6" source="a-service-verzending" target="a-app-tms"/>
    <!-- Integration / Interfaces -->
    <element xsi:type="archimate:FlowRelationship" id="r-7" source="a-app-api" target="a-app-erp"/>
    <element xsi:type="archimate:FlowRelationship" id="r-8" source="a-app-wms" target="a-app-tms"/>
    <element xsi:type="archimate:FlowRelationship" id="r-9" source="a-app-tms" target="a-app-erp"/>
    <element xsi:type="archimate:FlowRelationship" id="r-10" source="a-app-crm" target="a-app-erp"/>
    <!-- Technology hosting -->
    <element xsi:type="archimate:ServingRelationship" id="r-11" source="t-node-wms" target="a-app-wms"/>
    <element xsi:type="archimate:ServingRelationship" id="r-12" source="t-node-cloud" target="a-app-erp"/>
  </folder>
  <folder name="Views" id="f-views" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix Architectuur - Default View" id="v-logistix-default">
      <!-- Business layer objects -->
      <child xsi:type="archimate:DiagramObject" id="d-bp-order" archimateElement="b-process-orderverwerking"><bounds x="120" y="40" width="220" height="60"/></child>
      <child xsi:type="archimate:DiagramObject" id="d-bp-magazijn" archimateElement="b-process-magazijn"><bounds x="420" y="40" width="260" height="60"/></child>
      <child xsi:type="archimate:DiagramObject" id="d-bp-klanten" archimateElement="b-process-klantenservice"><bounds x="720" y="40" width="280" height="60"/></child>
      <!-- Application layer objects -->
      <child xsi:type="archimate:DiagramObject" id="d-app-erp" archimateElement="a-app-erp"><bounds x="140" y="180" width="220" height="70"/></child>
      <child xsi:type="archimate:DiagramObject" id="d-app-wms" archimateElement="a-app-wms"><bounds x="420" y="180" width="220" height="70"/></child>
      <child xsi:type="archimate:DiagramObject" id="d-app-tms" archimateElement="a-app-tms"><bounds x="740" y="180" width="220" height="70"/></child>
      <child xsi:type="archimate:DiagramObject" id="d-app-crm" archimateElement="a-app-crm"><bounds x="420" y="300" width="220" height="60"/></child>
      <child xsi:type="archimate:DiagramObject" id="d-app-api" archimateElement="a-app-api"><bounds x="20" y="180" width="100" height="40"/></child>
      <child xsi:type="archimate:DiagramObject" id="d-app-email" archimateElement="a-app-emailbatch"><bounds x="20" y="260" width="140" height="40"/></child>
      <!-- Technology layer -->
      <child xsi:type="archimate:DiagramObject" id="d-tech-wmsnode" archimateElement="t-node-wms"><bounds x="420" y="420" width="220" height="60"/></child>
      <child xsi:type="archimate:DiagramObject" id="d-tech-cloud" archimateElement="t-node-cloud"><bounds x="140" y="420" width="220" height="60"/></child>
    </element>
  </folder>
</archimate:model>
