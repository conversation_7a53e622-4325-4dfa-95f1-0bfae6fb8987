import os
import docx
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

def final_correct_report():
    # Pad naar het rapport
    report_path = "Adviesrapport Veranderingsmanagement-<PERSON><PERSON>-1066741.docx"
    visuals_folder = "Exported_Slides"
    
    if not os.path.exists(report_path):
        print(f"Rapport niet gevonden: {report_path}")
        return
    
    # Maak een nieuw document
    doc = docx.Document()
    
    print("Document wordt definitief gecorrigeerd...")
    
    # Voorpagina
    heading = doc.add_heading('ADVIESRAPPORT VERANDERINGSMANAGEMENT', 0)
    heading.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    
    doc.add_heading('Euro Caps - Implementatie Six Sigma & HACCP', 1)
    doc.add_paragraph('Door: Shu<PERSON> (1066741)')
    doc.add_paragraph('Hogeschool Rotterdam - Bedrijfskunde')
    doc.add_paragraph('Datum: Juli 2024')
    doc.add_page_break()
    
    # Voorwoord
    doc.add_heading('Voorwoord', level=1)
    voorwoord = (
        "Dit adviesrapport is geschreven als onderdeel van de opleiding Bedrijfskunde aan Hogeschool Rotterdam. "
        "De aanleiding is de toenemende druk op Euro Caps om te verduurzamen en procesverbeteringen door te voeren. "
        "Het rapport presenteert een uitgebreide veranderstrategie gericht op de implementatie van Six Sigma methodieken "
        "in combinatie met HACCP-principes voor voedselveiligheid.\n\n"
        "Dank aan alle betrokkenen bij Euro Caps voor hun input en ondersteuning tijdens het onderzoek."
    )
    doc.add_paragraph(voorwoord)
    doc.add_page_break()
    
    # Managementsamenvatting
    doc.add_heading('Managementsamenvatting', level=1)
    samenvatting = (
        "Euro Caps staat voor de uitdaging om haar productieprocessen te optimaliseren en te verduurzamen. "
        "Dit adviesrapport presenteert een integrale veranderstrategie die Six Sigma methodieken combineert "
        "met HACCP-principes voor voedselveiligheid.\n\n"
        "De belangrijkste aanbevelingen zijn:\n"
        "• Implementatie van het Kotter 8-stappenmodel voor verandermanagement\n"
        "• Toepassing van Six Sigma DMAIC-framework met HACCP-integratie\n"
        "• Uitgebreide stakeholderanalyse en communicatiestrategie\n"
        "• Invoering van zelfsturende teams en continue verbetering\n\n"
        "Verwachte resultaten: 30% reductie in defecten, 15% verbetering in procesefficiëntie, "
        "en 100% compliance met voedselveilheidsnormen."
    )
    doc.add_paragraph(samenvatting)
    doc.add_page_break()
    
    # Inhoudsopgave
    doc.add_heading('Inhoudsopgave', level=1)
    toc_items = [
        "1. Inleiding",
        "2. Theoretisch Kader",
        "3. Huidige Situatie Analyse",
        "4. Stakeholderanalyse", 
        "5. Gewenste Situatie",
        "6. Veranderstrategie",
        "7. Implementatieplan",
        "8. Communicatieplan",
        "9. Risicoanalyse",
        "10. Conclusie",
        "11. Aanbevelingen",
        "Literatuurlijst",
        "Bijlagen"
    ]
    
    for item in toc_items:
        p = doc.add_paragraph()
        p.add_run('• ' + item)
    
    doc.add_page_break()
    
    # Hoofdstuk 1: Inleiding
    doc.add_heading('1. Inleiding', level=1)
    doc.add_heading('1.1 Aanleiding', level=2)
    aanleiding = (
        "Euro Caps, een toonaangevende producent van koffiecapsules, staat voor de uitdaging om haar "
        "productieprocessen te optimaliseren en te verduurzamen. De toenemende marktdruk, strengere "
        "voedselveiligheidsnormen en de wens tot duurzame productie maken een fundamentele verandering noodzakelijk.\n\n"
        "Deze verandering vereist niet alleen technische aanpassingen, maar ook een cultuurverandering "
        "binnen de organisatie. Het implementeren van Six Sigma methodieken in combinatie met HACCP-principes "
        "vormt de kern van de voorgestelde veranderstrategie."
    )
    doc.add_paragraph(aanleiding)
    
    doc.add_heading('1.2 Probleemstelling', level=2)
    probleemstelling = (
        "Hoe kan Euro Caps haar productieprocessen succesvol optimaliseren door middel van "
        "Six Sigma methodieken en HACCP-principes, waarbij zowel procesefficiëntie als voedselveiligheid "
        "worden gewaarborgd, en hoe kan deze verandering worden geïmplementeerd met behoud van "
        "medewerkerstevredenheid en organisatiecultuur?"
    )
    doc.add_paragraph(probleemstelling)
    
    doc.add_heading('1.3 Doelstellingen', level=2)
    doelstellingen = [
        "Ontwikkelen van een integrale veranderstrategie voor Euro Caps",
        "Implementeren van Six Sigma methodieken voor procesoptimalisatie",
        "Integreren van HACCP-principes voor voedselveiligheid",
        "Creëren van een cultuur van continue verbetering",
        "Verhogen van medewerkerstevredenheid en betrokkenheid",
        "Behouden van hoge kwaliteitsstandaarden tijdens verandering"
    ]
    
    for doel in doelstellingen:
        p = doc.add_paragraph()
        p.add_run('• ' + doel)
    
    doc.add_page_break()
    
    # Hoofdstuk 2: Theoretisch Kader
    doc.add_heading('2. Theoretisch Kader', level=1)
    doc.add_heading('2.1 Verandermanagement Modellen', level=2)
    
    doc.add_heading('2.1.1 Kotter 8-stappenmodel', level=3)
    kotter_uitleg = (
        "Het model van Kotter (1996) biedt een systematische aanpak voor organisatieverandering. "
        "De acht stappen zijn: urgentiebesef creëren, coalitie vormen, visie ontwikkelen, "
        "visie communiceren, obstakels wegnemen, korte termijn successen behalen, "
        "verandering verankeren, en nieuwe cultuur institutionaliseren."
    )
    doc.add_paragraph(kotter_uitleg)
    
    # Verwijzing naar internet visual voor Kotter
    doc.add_paragraph("Zie voor een visuele weergave van het Kotter 8-stappenmodel: https://www.kotterinc.com/8-steps-process-for-leading-change/")
    
    doc.add_heading('2.1.2 Caluwé Kleurenmodel', level=3)
    caluwe_uitleg = (
        "Het kleurenmodel van De Caluwé (2006) categoriseert verschillende benaderingen van verandering: "
        "blauw (planmatig), geel (politiek), rood (mensen), groen (leren), en wit (chaos). "
        "Voor Euro Caps wordt een combinatie van blauw en groen aanbevolen."
    )
    doc.add_paragraph(caluwe_uitleg)
    
    # Verwijzing naar internet visual voor Caluwé
    doc.add_paragraph("Zie voor een visuele weergave van het Caluwé Kleurenmodel: https://www.veranderkunde.nl/kleurenmodel-de-caluwe/")
    
    doc.add_heading('2.2 Six Sigma & HACCP', level=2)
    sixsigma_uitleg = (
        "Six Sigma is een datagedreven methodiek voor procesverbetering, gebaseerd op het DMAIC-framework: "
        "Define, Measure, Analyze, Improve, Control. HACCP (Hazard Analysis and Critical Control Points) "
        "is een systematische aanpak voor voedselveiligheid. De integratie van beide methodieken "
        "zorgt voor zowel procesoptimalisatie als voedselveiligheid."
    )
    doc.add_paragraph(sixsigma_uitleg)
    
    # Verwijzing naar internet visual voor Six Sigma DMAIC
    doc.add_paragraph("Zie voor een visuele weergave van het Six Sigma DMAIC-framework: https://www.isixsigma.com/dmaic/")
    
    doc.add_page_break()
    
    # Hoofdstuk 3: Huidige Situatie
    doc.add_heading('3. Huidige Situatie Analyse', level=1)
    doc.add_heading('3.1 Organisatiecontext', level=2)
    context = (
        "Euro Caps is een middelgrote organisatie met een traditionele hiërarchische structuur. "
        "De organisatie heeft een sterke focus op kwaliteit en voedselveiligheid, maar kampt met "
        "inefficiënte processen en beperkte innovatie. De huidige cultuur is gericht op controle "
        "en standaardisatie, wat innovatie en medewerkerbetrokkenheid beperkt."
    )
    doc.add_paragraph(context)
    
    doc.add_heading('3.2 Belangrijkste Uitdagingen', level=2)
    uitdagingen = [
        "Inefficiënte productieprocessen met hoge variatie",
        "Beperkte medewerkerbetrokkenheid bij verbeteringen",
        "Gebrek aan systematische procesanalyse",
        "Reactieve in plaats van proactieve kwaliteitscontrole",
        "Beperkte innovatie en experimenteerruimte",
        "Fragmenteerde communicatie tussen afdelingen"
    ]
    
    for uitdaging in uitdagingen:
        p = doc.add_paragraph()
        p.add_run('• ' + uitdaging)
    
    doc.add_page_break()
    
    # Hoofdstuk 4: Stakeholderanalyse
    doc.add_heading('4. Stakeholderanalyse', level=1)
    doc.add_paragraph("Voor een succesvolle implementatie is het essentieel om een helder beeld te hebben van de betrokken stakeholders.")
    
    # Voeg stakeholder visual toe VAN DE SLIDE
    stakeholder_visual = os.path.join(visuals_folder, "4. Stakeholderanalyse/slide/Slide4.PNG")
    if os.path.exists(stakeholder_visual):
        doc.add_picture(stakeholder_visual, width=Inches(6))
        doc.add_paragraph("Figuur 1: Stakeholder Matrix Euro Caps")
    
    doc.add_heading('4.1 Overzicht Stakeholders', level=2)
    
    # Stakeholder tabel
    table = doc.add_table(rows=1, cols=7)
    table.style = 'Table Grid'
    hdr_cells = table.rows[0].cells
    headers = ['Stakeholdergroep', 'Naam stakeholder', 'Functie', 'Betrokkenheid', 'Belang', 'Positie', 'Invloed']
    for i, h in enumerate(headers):
        hdr_cells[i].text = h
    
    stakeholders = [
        ['Bedrijfsvoering', 'Servé Bosland', 'Manager Bedrijfsvoering', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
        ['Projectleiding', 'Niene Tepe', 'Projectleider', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
        ['Directie', 'Nils Clement', 'CEO', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
        ['ICT', 'Erik Dekker', 'Manager ICT', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
        ['Financiën', 'Berkan Arrindell', 'Hoofd Financiën', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
        ['Productie', 'Ismail Berenschot', 'Productiemedewerker', 'Gemiddeld', 'Gemiddeld', 'Blocker', 'Laag'],
        ['Productie', 'Samantha Mukhlis Aswad', 'Productiemedewerker', 'Gemiddeld', 'Gemiddeld', 'Blocker', 'Laag'],
        ['Logistiek', 'Rijk Wegen', 'Manager Logistiek', 'Gemiddeld', 'Hoog', 'Floater', 'Hoog'],
        ['Inkoop', 'Ko Jager', 'Manager Inkoop', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
        ['Logistiek', 'Tila Karren', 'Medewerker Logistiek', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
        ['Kwaliteitsbeheer', 'Kees Keurig', 'Hoofd Kwaliteitsbeheer', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
    ]
    
    for row in stakeholders:
        row_cells = table.add_row().cells
        for i, item in enumerate(row):
            row_cells[i].text = item
    
    doc.add_paragraph()
    
    doc.add_heading('4.2 Positie-categorieën', level=2)
    uitleg = (
        "De positie-categorie (Mover, Blocker, Floater) geeft inzicht in hoe elke stakeholder zich opstelt ten opzichte van het verandertraject: "
        "een Mover is actief betrokken, denkt mee en stimuleert de verandering; een Blocker is kritisch of terughoudend en kan de verandering belemmeren; "
        "een Floater is neutraal en beweegt mee met de situatie. Deze classificatie helpt bij het bepalen van de juiste communicatie- en interventiestrategie."
    )
    doc.add_paragraph(uitleg)
    
    doc.add_page_break()
    
    # Hoofdstuk 5: Gewenste Situatie
    doc.add_heading('5. Gewenste Situatie', level=1)
    doc.add_heading('5.1 Visie', level=2)
    visie = (
        "Euro Caps streeft naar een organisatie die wordt gekenmerkt door:\n"
        "• Continue procesverbetering en innovatie\n"
        "• Hoge kwaliteitsstandaarden en voedselveiligheid\n"
        "• Betrokken en gemotiveerde medewerkers\n"
        "• Efficiënte en duurzame productieprocessen\n"
        "• Klanttevredenheid en marktleiderschap"
    )
    doc.add_paragraph(visie)
    
    doc.add_heading('5.2 Doelstellingen', level=2)
    doelen = [
        "30% reductie in productiedefecten binnen 12 maanden",
        "15% verbetering in procesefficiëntie",
        "100% compliance met voedselveilheidsnormen",
        "Verhoging van medewerkertevredenheid met 20%",
        "Implementatie van zelfsturende teams",
        "Reductie van 25% in procesvariatie"
    ]
    
    for doel in doelen:
        p = doc.add_paragraph()
        p.add_run('• ' + doel)
    
    doc.add_page_break()
    
    # Hoofdstuk 6: Veranderstrategie
    doc.add_heading('6. Veranderstrategie', level=1)
    doc.add_heading('6.1 Gekozen Aanpak', level=2)
    aanpak = (
        "De veranderstrategie combineert het Kotter 8-stappenmodel met Six Sigma DMAIC-framework "
        "en HACCP-principes. Deze integrale aanpak zorgt voor:\n\n"
        "• Systematische verandering volgens bewezen modellen\n"
        "• Datagedreven procesverbetering\n"
        "• Waarborging van voedselveiligheid\n"
        "• Betrokkenheid van alle stakeholders\n"
        "• Duurzame resultaten"
    )
    doc.add_paragraph(aanpak)
    
    doc.add_heading('6.2 Implementatiefasen', level=2)
    fasen = [
        "Fase 1: Voorbereiding (maanden 1-2) - Urgentiebesef en coalitie",
        "Fase 2: Analyse (maanden 3-4) - Huidige situatie en doelen",
        "Fase 3: Ontwerp (maanden 5-6) - Nieuwe processen en systemen",
        "Fase 4: Implementatie (maanden 7-10) - Uitvoering en training",
        "Fase 5: Borging (maanden 11-12) - Monitoring en verankering"
    ]
    
    for fase in fasen:
        p = doc.add_paragraph()
        p.add_run('• ' + fase)
    
    doc.add_page_break()
    
    # Hoofdstuk 7: Implementatieplan
    doc.add_heading('7. Implementatieplan', level=1)
    doc.add_heading('7.1 Activiteiten en Tijdlijn', level=2)
    
    # Implementatie tabel
    table = doc.add_table(rows=1, cols=4)
    table.style = 'Table Grid'
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Fase'
    hdr_cells[1].text = 'Activiteiten'
    hdr_cells[2].text = 'Verantwoordelijke'
    hdr_cells[3].text = 'Tijdlijn'
    
    data = [
        ['Fase 1', 'Urgentiebesef creëren\nCoalitie vormen', 'CEO & Projectleider', 'Maand 1-2'],
        ['Fase 2', 'Visie ontwikkelen\nCommuniceren', 'Directie & MT', 'Maand 3-4'],
        ['Fase 3', 'Six Sigma training\nHACCP implementatie', 'Kwaliteitsmanager & ICT', 'Maand 5-6'],
        ['Fase 4', 'Procesverbeteringen\nTeamtraining', 'Alle afdelingen', 'Maand 7-10'],
        ['Fase 5', 'Monitoring\nBorging', 'Kwaliteitsmanager', 'Maand 11-12']
    ]
    
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = cell_data
    
    doc.add_page_break()
    
    # Hoofdstuk 8: Communicatieplan
    doc.add_heading('8. Communicatieplan', level=1)
    doc.add_heading('8.1 Communicatiestrategie', level=2)
    comm_strategie = (
        "Het communicatieplan is afgestemd op de verschillende stakeholdergroepen en volgt "
        "de principes van effectieve verandercommunicatie. Per stakeholder wordt een specifieke "
        "aanpak gehanteerd met passende communicatiekanalen en frequentie."
    )
    doc.add_paragraph(comm_strategie)
    
    doc.add_heading('8.2 Communicatiekanalen', level=2)
    kanalen = [
        "Directe communicatie: Teammeetings, workshops, trainingen",
        "Schriftelijke communicatie: Nieuwsbrieven, rapportages, handleidingen",
        "Digitale communicatie: Intranet, e-mail, dashboards",
        "Visuele communicatie: Posters, infographics, presentaties"
    ]
    
    for kanaal in kanalen:
        p = doc.add_paragraph()
        p.add_run('• ' + kanaal)
    
    doc.add_page_break()
    
    # Hoofdstuk 9: Risicoanalyse
    doc.add_heading('9. Risicoanalyse', level=1)
    doc.add_heading('9.1 Geïdentificeerde Risico\'s', level=2)
    
    # Risico tabel
    risk_table = doc.add_table(rows=1, cols=4)
    risk_table.style = 'Table Grid'
    risk_hdr = risk_table.rows[0].cells
    risk_hdr[0].text = 'Risico'
    risk_hdr[1].text = 'Waarschijnlijkheid'
    risk_hdr[2].text = 'Impact'
    risk_hdr[3].text = 'Mitigatie'
    
    risks = [
        ['Weerstand tegen verandering', 'Hoog', 'Hoog', 'Uitgebreide communicatie en betrokkenheid'],
        ['Tijdsdruk en overbelasting', 'Gemiddeld', 'Hoog', 'Realistische planning en ondersteuning'],
        ['Technische problemen', 'Laag', 'Gemiddeld', 'Expertise en backup-plannen'],
        ['Budgetoverschrijding', 'Gemiddeld', 'Gemiddeld', 'Strikte budgetcontrole'],
        ['Verlies van kennis', 'Laag', 'Hoog', 'Kennisborging en documentatie']
    ]
    
    for risk in risks:
        risk_row = risk_table.add_row().cells
        for i, risk_data in enumerate(risk):
            risk_row[i].text = risk_data
    
    doc.add_page_break()
    
    # Hoofdstuk 10: Conclusie
    doc.add_heading('10. Conclusie', level=1)
    conclusie = (
        "De voorgestelde veranderstrategie biedt Euro Caps een systematische en bewezen aanpak "
        "voor het optimaliseren van haar productieprocessen. Door de combinatie van Six Sigma "
        "methodieken en HACCP-principes worden zowel procesefficiëntie als voedselveiligheid gewaarborgd.\n\n"
        "De implementatie van het Kotter 8-stappenmodel zorgt voor een gestructureerde verandering "
        "met betrokkenheid van alle stakeholders. De verwachte resultaten zijn significant en "
        "dragen bij aan de duurzame groei en concurrentiepositie van Euro Caps."
    )
    doc.add_paragraph(conclusie)
    
    doc.add_page_break()
    
    # Hoofdstuk 11: Aanbevelingen
    doc.add_heading('11. Aanbevelingen', level=1)
    doc.add_heading('11.1 Prioriteiten voor Implementatie', level=2)
    
    aanbevelingen = [
        "Start direct met het creëren van urgentiebesef onder alle medewerkers",
        "Formeer een sterke coalitie van voorstanders uit alle afdelingen",
        "Investeer in uitgebreide training en begeleiding voor alle medewerkers",
        "Zorg voor voldoende middelen en tijd voor de implementatie",
        "Implementeer een robuust monitoring- en evaluatiesysteem",
        "Plan regelmatige evaluatiemomenten en bijsturing",
        "Zorg voor continue communicatie en transparantie",
        "Borg de verandering in de organisatiecultuur en -structuur"
    ]
    
    for aanbeveling in aanbevelingen:
        p = doc.add_paragraph()
        p.add_run('• ' + aanbeveling)
    
    doc.add_page_break()
    
    # Literatuurlijst
    doc.add_heading('Literatuurlijst', level=1)
    literatuur = [
        "Kotter, J. P. (1996). Leading Change. Boston: Harvard Business School Press.",
        "Caluwé, L. de, & Vermaak, H. (2006). Leren veranderen: Een handboek voor de veranderkundige. Deventer: Kluwer.",
        "George, M. L., Rowlands, D., Price, M., & Maxey, J. (2005). The Lean Six Sigma Pocket Toolbook. New York: McGraw-Hill.",
        "Codex Alimentarius. (2003). Hazard Analysis and Critical Control Point (HACCP) System and Guidelines for its Application. Rome: FAO/WHO.",
        "Freeman, R. E. (1984). Strategic Management: A Stakeholder Approach. Boston: Pitman.",
        "Hofstede, G. (2010). Cultures and Organizations: Software of the Mind. New York: McGraw-Hill.",
        "Mintzberg, H. (1979). The Structuring of Organizations. Englewood Cliffs: Prentice-Hall.",
        "Schein, E. H. (2010). Organizational culture and leadership. San Francisco: Jossey-Bass."
    ]
    
    for bron in literatuur:
        p = doc.add_paragraph()
        p.add_run('• ' + bron)
    
    doc.add_page_break()
    
    # Bijlagen
    doc.add_heading('Bijlagen', level=1)
    doc.add_heading('Bijlage A: Organisatiestructuur', level=2)
    doc.add_paragraph("Zie Visual_1_Organisatiestructuur_Euro_Caps.txt")
    
    doc.add_heading('Bijlage B: DMAIC-HACCP Integratie', level=2)
    doc.add_paragraph("Zie Visual_3_DMAIC_HACCP_Integratie.txt")
    
    # Sla het document op
    doc.save(report_path)
    print(f"\nDocument definitief gecorrigeerd: {report_path}")
    print("✓ Alleen stakeholder visual van slide gebruikt")
    print("✓ Verwijzingen naar internet visuals voor strategieën")
    print("✓ Geen dubbele content")
    print("✓ Alles in één document")

if __name__ == "__main__":
    final_correct_report() 