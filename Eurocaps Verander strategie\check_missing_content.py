import os
import glob

def analyze_available_content():
    print("=== ANALYSE VAN BESCHIKBARE CONTENT VOOR ADVIESRAPPORT ===\n")
    
    # Beschikbare bestanden
    files = {
        "Adviesrapporten": glob.glob("*.docx") + glob.glob("*.pdf"),
        "Stakeholder Data": glob.glob("*stakeholder*") + glob.glob("*Stakeholder*"),
        "Visuals": glob.glob("Exported_Slides/**/*.PNG", recursive=True),
        "Scripts": glob.glob("*.py"),
        "Ondersteunende Documenten": glob.glob("*.txt") + glob.glob("*.rtf") + glob.glob("*.xlsx")
    }
    
    print("1. BESCHIKBARE BESTANDEN:")
    for category, file_list in files.items():
        print(f"\n{category}:")
        for file in file_list:
            print(f"  - {file}")
    
    # Analyse van PowerPoint content
    print("\n\n2. POWERPOINT CONTENT (geëxporteerd als PNG):")
    pptx_content = {
        "Verandermanagement": "0. Verandermanagement",
        "Waarom veranderen": "1. Waarom veranderen", 
        "Kotter Model": "2. Kotter",
        "Veranderaanpak": "2. Veranderaanpak",
        "Cultuur in organisaties": "3. Cultuur in organisaties",
        "Gedrag in organisaties": "3. Gedrag in organisaties",
        "Stakeholderanalyse": "4. Stakeholderanalyse",
        "Caluwé Model": "5. Caluwe",
        "Waarom is veranderen lastig": "5. Waarom is veranderen lastig",
        "Communicatieplan": "6. Communicatieplan",
        "Interventies": "6. Interventies",
        "Bedrijfskunde Modules": ["Bedrijfskunde 4.1", "Bedrijfskunde 4.2", "Bedrijfskunde 4.4", "Bedrijfskunde 4.5", "Bedrijfskunde 4.6"]
    }
    
    for topic, folder in pptx_content.items():
        if isinstance(folder, list):
            print(f"\n{topic}:")
            for f in folder:
                if os.path.exists(f"Exported_Slides/{f}"):
                    slide_count = len(glob.glob(f"Exported_Slides/{f}/slide/*.PNG"))
                    print(f"  - {f}: {slide_count} slides")
        else:
            if os.path.exists(f"Exported_Slides/{folder}"):
                slide_count = len(glob.glob(f"Exported_Slides/{folder}/slide/*.PNG"))
                print(f"  - {topic}: {slide_count} slides")
    
    # Checklist voor rapport
    print("\n\n3. CHECKLIST VOOR COMPLET ADVIESRAPPORT:")
    
    essential_sections = [
        "Voorwoord",
        "Managementsamenvatting", 
        "Inleiding en Probleemstelling",
        "Theoretisch Kader",
        "Huidige Situatie Analyse",
        "Stakeholderanalyse",
        "Gewenste Situatie",
        "Veranderstrategie",
        "Implementatieplan",
        "Communicatieplan",
        "Risicoanalyse",
        "Conclusie",
        "Aanbevelingen",
        "Literatuurlijst",
        "Bijlagen"
    ]
    
    print("\nEssentiële secties die in het rapport moeten staan:")
    for section in essential_sections:
        print(f"  ☐ {section}")
    
    # Aanbevelingen voor ontbrekende content
    print("\n\n4. AANBEVELINGEN VOOR ONTBREKENDE CONTENT:")
    
    recommendations = [
        "Controleer of alle theoretische modellen (Kotter, Caluwé, Lewin) volledig zijn uitgewerkt",
        "Voeg een gedetailleerde risicoanalyse toe met mitigatiestrategieën",
        "Zorg voor een concrete tijdlijn voor implementatie",
        "Voeg een budgetraming toe voor de verandering",
        "Beschrijf de gewenste organisatiecultuur in detail",
        "Voeg een monitoring- en evaluatieplan toe",
        "Zorg voor een duidelijke communicatiestrategie per stakeholder",
        "Voeg praktische interventies toe met concrete voorbeelden",
        "Beschrijf de verwachte resultaten en KPI's",
        "Voeg een plan toe voor borging van de verandering"
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"  {i}. {rec}")
    
    print("\n\n5. VOLGENDE STAPPEN:")
    print("  1. Open het rapport en controleer welke secties ontbreken")
    print("  2. Voeg ontbrekende theoretische onderbouwing toe")
    print("  3. Verwerk de geëxporteerde visuals in de juiste secties")
    print("  4. Zorg voor een consistente opmaak en structuur")
    print("  5. Controleer spelling en grammatica")
    print("  6. Voeg paginanummers en inhoudsopgave toe")

if __name__ == "__main__":
    analyze_available_content() 