import re
from docx import Document
from docx.shared import Pt
from docx.oxml.ns import qn
from docx.enum.style import WD_STYLE_TYPE
import os

def clean_heading(text):
    # Verwijder sterretjes en overbodige tekens, behoud nummering
    text = text.strip()
    text = re.sub(r'^\*+\s*', '', text)
    text = re.sub(r'\*+$', '', text)
    return text

def convert_doc3_to_current_format():
    # Laad de tekst uit (3)
    with open("Adviesrapport Veranderingsmanagement-Shuja Schadon-1066741(3)_Extracted.txt", "r", encoding="utf-8") as f:
        lines = f.readlines()

    # Maak een nieuw document aan
    doc = Document()

    # Standaard stijl instellen op Arial 12pt
    style = doc.styles['Normal']
    style.font.name = 'Arial'
    style.font.size = Pt(12)
    style.element.rPr.rFonts.set(qn('w:eastAsia'), 'Arial')

    # Ko<PERSON><PERSON><PERSON> stijlen aanpassen
    for i in range(1, 5):
        style_name = f'Heading {i}'
        if style_name in doc.styles:
            h_style = doc.styles[style_name]
            h_style.font.name = 'Arial'
            h_style.font.size = Pt(18 if i == 1 else 16 if i == 2 else 14 if i == 3 else 13)
            h_style.element.rPr.rFonts.set(qn('w:eastAsia'), 'Arial')

    # Huidige hoofdstuk/sectie tracking
    current_heading_level = 0
    for line in lines:
        line = line.rstrip("\n")
        # Hoofdstuk of paragraaf kop
        match = re.match(r'^(\d+(?:\.\d+)*)(?:\s+|:|\.)?(.*)', line)
        if match:
            nummer = match.group(1)
            titel = match.group(2).strip()
            if titel:
                doc.add_heading(f"{nummer} {titel}", level=nummer.count('.'))
            else:
                doc.add_heading(f"{nummer}", level=nummer.count('.'))
            continue
        # Subkopjes met sterretjes
        if line.strip().startswith('**') and line.strip().endswith('**'):
            heading = clean_heading(line)
            doc.add_heading(heading, level=2)
            continue
        # Visuals
        if '(Visual' in line or '[Visual' in line:
            # Plaats een marker voor de visual (later vervangen door echte visual)
            doc.add_paragraph('[VISUAL HIER]')
            continue
        # Lege regel
        if not line.strip():
            doc.add_paragraph()
            continue
        p = doc.add_paragraph(line)
        p.style = 'Normal'

    # Opslaan als tussenversie
    doc.save("Adviesrapport_Veranderingsmanagement_OMGEZET.docx")
    print("Conversie naar huidige format voltooid. Arial 12pt toegepast. Tussenversie opgeslagen.")

if __name__ == "__main__":
    convert_doc3_to_current_format() 