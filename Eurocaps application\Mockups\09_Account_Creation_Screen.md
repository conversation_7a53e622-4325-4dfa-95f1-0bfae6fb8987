# Account Creation Screen - EuroCaps Ordering System

## Screen Layout

```
+---------------------------------------------------------------+
| [Logo] EuroCaps Ordering System                    [← Back]  |
+---------------------------------------------------------------+
|                                                               |
|                    CREATE NEW ACCOUNT                        |
|                                                               |
|  +-------------------------------------------------------+    |
|  |                 ACCOUNT INFORMATION                   |    |
|  |                                                       |    |
|  |  Username: *     [____________________]               |    |
|  |  Email: *        [____________________]               |    |
|  |  Password: *     [____________________]               |    |
|  |  Confirm Pass: * [____________________]               |    |
|  |                                                       |    |
|  |                 PERSONAL INFORMATION                  |    |
|  |                                                       |    |
|  |  First Name: *   [____________________]               |    |
|  |  Last Name: *    [____________________]               |    |
|  |  Phone Number:   [____________________]               |    |
|  |                                                       |    |
|  |                 COMPANY INFORMATION                   |    |
|  |                                                       |    |
|  |  Company Name: * [____________________]               |    |
|  |  Company Type:   [Retail ▼]                          |    |
|  |  VAT Number:     [____________________]               |    |
|  |                                                       |    |
|  |                 ADDRESS INFORMATION                   |    |
|  |                                                       |    |
|  |  Street Address: [____________________]               |    |
|  |  City: *         [____________________]               |    |
|  |  Postal Code: *  [____________________]               |    |
|  |  Country: *      [Netherlands ▼]                     |    |
|  |                                                       |    |
|  |  □ I agree to the Terms and Conditions               |    |
|  |  □ I want to receive marketing emails                |    |
|  |                                                       |    |
|  |  [CREATE ACCOUNT]              [CLEAR FORM]          |    |
|  +-------------------------------------------------------+    |
|                                                               |
|  Already have an account? [Sign In]                          |
|                                                               |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Background: Light gray (#f5f5f5)
- Form container: White (#ffffff)
- Required field indicators: Red (#e74c3c)
- Create Account button: Orange (#F39C12)
- Clear Form button: Gray (#95a5a6)
- Sign In link: Blue (#3498db)

### Typography
- Header: Arial, 16pt, Bold, White
- Page title: Arial, 24pt, Bold, Dark gray
- Section headers: Arial, 14pt, Bold, Dark gray
- Field labels: Arial, 12pt, Dark gray
- Required indicators (*): Arial, 12pt, Bold, Red
- Button text: Arial, 14pt, Bold, White
- Link text: Arial, 12pt, Blue

### Form Sections

1. **Account Information**
   - Username (required, unique)
   - Email (required, unique, validated)
   - Password (required, minimum 8 characters)
   - Confirm Password (required, must match)

2. **Personal Information**
   - First Name (required)
   - Last Name (required)
   - Phone Number (optional, validated format)

3. **Company Information**
   - Company Name (required)
   - Company Type (dropdown: Retail, Wholesale, Restaurant, Office)
   - VAT Number (optional, validated format)

4. **Address Information**
   - Street Address (optional)
   - City (required)
   - Postal Code (required, Dutch format validation)
   - Country (dropdown, default: Netherlands)

5. **Agreements**
   - Terms and Conditions (required checkbox)
   - Marketing emails (optional checkbox)

## Validation Rules

### Username
- Minimum 3 characters
- Maximum 20 characters
- Alphanumeric and underscore only
- Must be unique
- Cannot contain spaces

### Email
- Valid email format
- Must be unique in system
- Maximum 100 characters

### Password
- Minimum 8 characters
- Must contain at least one uppercase letter
- Must contain at least one lowercase letter
- Must contain at least one number
- Must contain at least one special character

### Phone Number
- Dutch phone number format: +31 XX XXX XXXX
- Optional field
- Auto-format as user types

### Postal Code
- Dutch postal code format: 1234 AB
- Required field
- Auto-format as user types

### VAT Number
- Dutch VAT format: NL123456789B01
- Optional field
- Validation against format

## Interactions

1. **Real-time Validation**
   - Username availability check (after 3 characters)
   - Email format validation
   - Password strength indicator
   - Confirm password match indicator
   - Phone number format validation
   - Postal code format validation

2. **Form Submission**
   - Validate all required fields
   - Check Terms and Conditions agreement
   - Create user account
   - Send welcome email
   - Navigate to login screen with success message

3. **Clear Form**
   - Reset all fields to default values
   - Clear all validation messages
   - Focus on first field

4. **Navigation**
   - Back button returns to login screen
   - Sign In link navigates to login screen
   - Successful creation navigates to login with pre-filled username

## Error Handling

### Field-level Errors
- Show red border and error message below field
- Clear error when field is corrected
- Real-time validation feedback

### Form-level Errors
- Show error summary at top of form
- Highlight problematic fields
- Scroll to first error field

### Common Error Messages
- "Username already exists"
- "Email address already registered"
- "Passwords do not match"
- "Please accept Terms and Conditions"
- "Invalid phone number format"
- "Invalid postal code format"

## Success Flow

1. **Account Created Successfully**
   - Show success notification
   - Send welcome email to user
   - Create customer record in database
   - Navigate to login screen
   - Pre-fill username field
   - Show "Account created successfully" message

2. **Welcome Email Content**
   - Welcome message
   - Account details (username, email)
   - Login instructions
   - Contact information for support

## Accessibility Considerations

- Clear visual hierarchy with section headers
- Required field indicators clearly marked
- Sufficient color contrast for all elements
- Keyboard navigation support
- Screen reader friendly labels
- Error messages clearly associated with fields

## Mobile Responsiveness

- Single column layout on mobile
- Larger touch targets for buttons
- Optimized keyboard types for different fields
- Collapsible sections to reduce scrolling

## Security Considerations

- Password hashing before storage
- Email verification (optional enhancement)
- Rate limiting for account creation
- CAPTCHA for spam prevention (optional)
- Secure transmission of all data

## Notes for Implementation

- Use PowerApps forms for structured data entry
- Implement client-side validation for immediate feedback
- Server-side validation for security
- Store passwords securely (hashed)
- Consider email verification workflow
- Log account creation events for audit trail
