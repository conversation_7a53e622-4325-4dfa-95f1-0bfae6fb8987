import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor

def create_proper_table(doc, title, headers, data):
    """Create a properly formatted table"""
    
    # Add table title
    table_title = doc.add_paragraph()
    table_title_run = table_title.add_run(title)
    table_title_run.font.bold = True
    table_title_run.font.size = Pt(12)
    table_title_run.font.name = 'Arial'
    
    # Create table
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Add headers
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        # Make header bold
        for paragraph in hdr_cells[i].paragraphs:
            for run in paragraph.runs:
                run.font.bold = True
                run.font.size = Pt(11)
                run.font.name = 'Arial'
    
    # Add data rows
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
            # Set font size for data cells
            for paragraph in row_cells[i].paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(10)
                    run.font.name = 'Arial'
    
    doc.add_paragraph()  # Add space after table
    return table

def add_visual_placeholder(doc, caption_text):
    """Add visual placeholder with proper caption"""
    
    # Add placeholder text
    placeholder_para = doc.add_paragraph()
    placeholder_run = placeholder_para.add_run('[VISUAL PLACEHOLDER]')
    placeholder_run.font.size = Pt(12)
    placeholder_run.font.italic = True
    placeholder_run.font.name = 'Arial'
    placeholder_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add caption
    caption_paragraph = doc.add_paragraph()
    caption_run = caption_paragraph.add_run(caption_text)
    caption_run.font.size = Pt(10)
    caption_run.font.italic = True
    caption_run.font.name = 'Arial'
    caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()  # Add space after visual
    return True

def complete_chapter_2(doc):
    """Complete Chapter 2 with remaining sections"""
    
    # 2.5 Stakeholderanalyse
    doc.add_heading('2.5 Stakeholderanalyse', 2)
    
    stakeholder_text = """Stakeholderanalyse is een cruciale component van elk verandermanagementproces. Het identificeert alle partijen die invloed hebben op of beïnvloed worden door de voorgenomen verandering, en helpt bij het ontwikkelen van gerichte strategieën om hun steun te verkrijgen of hun weerstand te overwinnen.

Het proces van stakeholderanalyse omvat verschillende stappen:

1. Identificatie van stakeholders: Het systematisch in kaart brengen van alle interne en externe partijen die betrokken zijn bij of beïnvloed worden door de verandering.

2. Analyse van belangen: Het begrijpen van wat elke stakeholder belangrijk vindt en hoe de verandering hun belangen kan beïnvloeden.

3. Beoordeling van macht en invloed: Het inschatten van hoeveel invloed elke stakeholder heeft op het succes van de verandering.

4. Categorisatie: Het indelen van stakeholders in categorieën op basis van hun macht en belang, vaak weergegeven in een Power-Interest matrix.

5. Strategieontwikkeling: Het formuleren van specifieke benaderingen voor elke stakeholdergroep.

De Power-Interest matrix is een veelgebruikt instrument dat stakeholders indeelt in vier categorieën:
- Hoge macht, hoog belang (Key Players): Deze stakeholders moeten nauw betrokken worden bij de verandering
- Hoge macht, laag belang (Context Setters): Deze stakeholders moeten tevreden gehouden worden
- Lage macht, hoog belang (Subjects): Deze stakeholders moeten geïnformeerd worden
- Lage macht, laag belang (Crowd): Deze stakeholders vereisen minimale aandacht

Effectieve stakeholdermanagement vereist continue monitoring en aanpassing van strategieën naarmate het veranderingsproces vordert."""
    
    doc.add_paragraph(stakeholder_text)
    
    add_visual_placeholder(doc, 'Figuur 2.6: Power-Interest matrix voor stakeholderanalyse')
    
    # 2.6 Kübler-Ross
    doc.add_heading('2.6 Verandercurve van Kübler-Ross', 2)
    
    kubler_ross_text = """De verandercurve van Elisabeth Kübler-Ross (1969), oorspronkelijk ontwikkeld om de emotionele fasen van rouw te beschrijven, wordt in verandermanagement gebruikt om de typische emotionele reacties van mensen op verandering te begrijpen en te anticiperen.

De curve beschrijft vijf fasen die mensen doorgaans doorlopen wanneer zij geconfronteerd worden met significante verandering:

1. Ontkenning: De eerste reactie is vaak het ontkennen dat verandering nodig is of zal plaatsvinden. Mensen hopen dat de situatie vanzelf zal verbeteren of dat de verandering zal worden teruggedraaid.

2. Woede/Frustratie: Wanneer ontkenning niet langer mogelijk is, ontstaat vaak woede over de verandering. Deze fase wordt gekenmerkt door weerstand, kritiek en soms sabotage.

3. Onderhandeling: In deze fase proberen mensen de verandering te beïnvloeden of te beperken door voorwaarden te stellen of alternatieven voor te stellen.

4. Depressie/Acceptatie van verlies: Wanneer duidelijk wordt dat de verandering onvermijdelijk is, kunnen mensen een periode van somberheid of verlies ervaren over de oude situatie.

5. Acceptatie: Uiteindelijk accepteren mensen de nieuwe situatie en beginnen zij zich aan te passen aan de verandering.

Het begrijpen van deze curve helpt verandermanagers om:
- Realistische verwachtingen te hebben over de tijd die nodig is voor verandering
- Passende ondersteuning te bieden in elke fase
- Weerstand te herkennen als een normale reactie
- Interventies af te stemmen op de emotionele behoeften van mensen

Het is belangrijk te realiseren dat niet iedereen alle fasen doorloopt, dat de volgorde kan variëren, en dat mensen in verschillende fasen kunnen zijn voor verschillende aspecten van dezelfde verandering."""
    
    doc.add_paragraph(kubler_ross_text)
    
    add_visual_placeholder(doc, 'Figuur 2.7: Kübler-Ross verandercurve met emotionele fasen tijdens verandering')
    
    return doc

def add_chapters_3_and_4(doc):
    """Add comprehensive chapters 3 and 4"""
    
    # Chapter 3: Current Situation
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 3: Huidige situatie', 1)
    
    intro_ch3 = """Dit hoofdstuk biedt een uitgebreide analyse van de huidige organisatorische context van Euro Caps. Door gebruik te maken van de theoretische modellen uit hoofdstuk 2, wordt een helder beeld geschetst van hoe de organisatie momenteel functioneert, zowel op structureel als cultureel niveau. Deze analyse vormt de basis voor het identificeren van de gewenste veranderingen en het ontwikkelen van een passende implementatiestrategie."""
    
    doc.add_paragraph(intro_ch3)
    
    doc.add_heading('3.1 Huidige organisatiestructuur', 2)
    
    current_structure = """De organisatiestructuur van Euro Caps vertoont kenmerken van wat Mintzberg (1983) beschrijft als een hybride vorm tussen een machineorganisatie en een innovatieve organisatie. Deze hybride structuur is ontstaan door de specifieke eisen van de koffiecapsule-industrie, waarbij zowel efficiënte massaproductie als continue innovatie vereist zijn.

Kenmerken van de machineorganisatie bij Euro Caps:
- Gestandaardiseerde productieprocessen voor koffiecapsules
- Duidelijke hiërarchische structuur met functionele afdelingen
- Formele procedures voor kwaliteitscontrole en veiligheid
- Efficiëntie-gerichte prestatiemeting
- Beperkte horizontale communicatie tussen afdelingen

Kenmerken van de innovatieve organisatie bij Euro Caps:
- Flexibele projectteams voor productontwikkeling
- Directe communicatie tussen specialisten
- Aanpassingsvermogen aan marktveranderingen
- Expertise-gebaseerde autoriteit in technische vraagstukken
- Experimenteren met nieuwe producten en processen

Deze hybride structuur biedt voordelen maar creëert ook uitdagingen voor de implementatie van Six Sigma. De gestandaardiseerde processen bieden een goede basis voor Six Sigma methodieken, maar de beperkte horizontale communicatie kan de cross-functionele samenwerking belemmeren die essentieel is voor succesvolle procesverbetering."""
    
    doc.add_paragraph(current_structure)
    
    # Add Mintzberg decision matrix
    mintzberg_headers = ['Organisatiestructuur', 'Standaardisatie', 'Innovatie', 'Flexibiliteit', 'Technologie', 'Efficiëntie', 'Totaal Score']
    mintzberg_data = [
        ['Ondernemende organisatie', '2', '5', '4', '3', '2', '16'],
        ['Machineorganisatie', '5', '1', '1', '3', '5', '15'],
        ['Professionele organisatie', '4', '2', '2', '2', '4', '14'],
        ['Innovatieve organisatie', '1', '5', '5', '4', '1', '16'],
        ['Hybride (Euro Caps)', '4', '4', '3', '4', '4', '19']
    ]
    
    create_proper_table(doc, 'Tabel 3.1: Beslissingsmatrix organisatiestructuur volgens Mintzberg', mintzberg_headers, mintzberg_data)
    
    add_visual_placeholder(doc, 'Figuur 3.1: Beslissingsmatrix voor organisatiestructuur volgens Mintzberg')
    
    return doc

if __name__ == "__main__":
    print("=== Completing Final Document ===")
    
    # Load existing document
    doc = Document('Adviesrapport_Veranderingsmanagement_UITGEBREID_DEEL2.docx')
    
    # Complete remaining sections
    doc = complete_chapter_2(doc)
    doc = add_chapters_3_and_4(doc)
    
    # Save updated document
    doc.save('Adviesrapport_Veranderingsmanagement_BIJNA_COMPLEET.docx')
    print("Document almost complete!")
    print("\n=== UITBREIDINGEN DEEL 3 ===")
    print("✅ Hoofdstuk 2 volledig afgerond")
    print("✅ Stakeholderanalyse theorie toegevoegd")
    print("✅ Kübler-Ross verandercurve uitgewerkt")
    print("✅ Hoofdstuk 3 begonnen met huidige situatie")
    print("✅ Mintzberg analyse met beslissingsmatrix")
