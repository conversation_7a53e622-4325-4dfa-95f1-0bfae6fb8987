import os
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

def add_visuals_to_document():
    """
    Add all available visuals to the document at appropriate locations
    """
    
    # Load the existing document
    doc_path = 'Adviesrapport_Veranderingsmanagement_VOLLEDIG_GEINTEGREERD_MET_VISUALS.docx'
    doc = Document(doc_path)
    
    # Define visual mappings - which visual goes where
    visual_mappings = {
        'Visual_1_Boonstra_Veranderstrategieen.png': '[Visual van Boonstra\'s veranderstrategieën hier',
        'Visual_2_Caluwe_Kleurenmodel.png': '[Visual van De Caluwé\'s kleurenmodel hier',
        'Visual_3_Gap_Analyse_Model.png': '[Visual van een Gap-analyse model hier',
        'Visual_4_Hofstede_Cultuurdimensies.png': '[Visual van Hofstede\'s cultuurdimensies hier',
        'Visual_5_Kotter_8_Stappenmodel.png': '[<PERSON> van Kotter\'s 8-stappenmodel hier',
        'Visual_6_Stakeholderanalyse_Matrix.png': '[Visual van een algemeen Stakeholderanalyse model hier',
        'Visual_7_Kubler_Ross_Verandercurve.png': '[Visual van de Verandercurve van Kübler-Ross hier',
        'Visual_8_Beslissingsmatrix_Mintzberg.png': '[Visual van Beslissingsmatrix Mintzberg hier',
        'Visual_9_Boonstra_Beslissingsmatrix.png': '[Visual van Boonstra\'s Beslissingsmatrix hier',
        'Visual_10_DMAIC_Kotter_Integratie.png': '[Visual van DMAIC en Kotter integratie hier'
    }
    
    # Additional visual mappings for stakeholder analysis
    stakeholder_visuals = {
        'Visual_Stakeholdermatrix.png': '[Visual van Stakeholdermatrix hier',
        'Visual_Stakeholdermap.png': 'Tabel 2: Overzicht stakeholders',
        'Visual_Stakeholdermanagementfasen.png': 'Tabel 3: Categorisatie van de stakeholders'
    }
    
    # Combine all mappings
    all_visuals = {**visual_mappings, **stakeholder_visuals}
    
    # Process each paragraph to find visual placeholders
    for i, paragraph in enumerate(doc.paragraphs):
        paragraph_text = paragraph.text
        
        # Check if this paragraph contains a visual placeholder
        for visual_file, placeholder_text in all_visuals.items():
            if placeholder_text in paragraph_text:
                try:
                    # Check if visual file exists
                    if os.path.exists(visual_file):
                        # Add the visual after this paragraph
                        new_paragraph = doc.paragraphs[i].insert_paragraph_before()
                        run = new_paragraph.runs[0] if new_paragraph.runs else new_paragraph.add_run()
                        
                        # Add the image
                        run.add_picture(visual_file, width=Inches(6))
                        new_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        
                        # Add caption
                        caption_paragraph = doc.paragraphs[i].insert_paragraph_before()
                        caption_paragraph.add_run(f"Figuur: {visual_file.replace('.png', '').replace('Visual_', '').replace('_', ' ')}")
                        caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        
                        print(f"Added visual: {visual_file} at paragraph {i}")
                        
                        # Update the placeholder text to indicate visual was added
                        paragraph.text = paragraph_text.replace(placeholder_text, f"[VISUAL TOEGEVOEGD: {visual_file}]")
                        
                    else:
                        print(f"Visual file not found: {visual_file}")
                        
                except Exception as e:
                    print(f"Error adding visual {visual_file}: {str(e)}")
    
    # Save the updated document
    output_path = 'Adviesrapport_Veranderingsmanagement_COMPLEET_MET_ALLE_VISUALS.docx'
    doc.save(output_path)
    print(f"Document saved as: {output_path}")
    
    return output_path

def create_visual_summary():
    """
    Create a summary of all available visuals
    """
    visual_files = [f for f in os.listdir('.') if f.startswith('Visual_') and f.endswith('.png')]
    
    print("Available visual files:")
    for i, visual in enumerate(visual_files, 1):
        print(f"{i}. {visual}")
    
    return visual_files

def add_missing_content_from_doc3():
    """
    Add any missing content from document (3) that wasn't included yet
    """
    
    # Load the document
    doc_path = 'Adviesrapport_Veranderingsmanagement_COMPLEET_MET_ALLE_VISUALS.docx'
    if not os.path.exists(doc_path):
        doc_path = 'Adviesrapport_Veranderingsmanagement_VOLLEDIG_GEINTEGREERD_MET_VISUALS.docx'
    
    doc = Document(doc_path)
    
    # Add the detailed Kotter implementation from document (3)
    kotter_implementation = """
    De implementatie van de Six Sigma aanpak bij Euro Caps zal gefaseerd plaatsvinden over een periode van 21 maanden, verdeeld in drie hoofdfasen, geïntegreerd met Kotter's acht stappen:

    **Fase 1: Voorbereiding (3 maanden)**
    Deze fase correspondeert met Kotter's stappen 1, 2, en 3. De focus ligt op het creëren van een stevige basis en een duidelijke routekaart voor de verandering.

    1. **Creëer een gevoel van urgentie:** De noodzaak van continue kwaliteitsverbetering en efficiëntie om concurrentievoordeel te behouden, wordt door CEO Nils Clement en Manager Bedrijfsvoering Servé Bosland intern benadrukt. Dit gebeurt door concrete cijfers over huidige verspillingen en gemiste kansen te presenteren, mede gebaseerd op Six Sigma metingen uit de 'Define' fase.

    2. **Vorm een leidende coalitie:** Een multidisciplinaire werkgroep wordt samengesteld, bestaande uit belangrijke stakeholders met voldoende autoriteit en expertise om de verandering te sturen en de Six Sigma implementatie te borgen.

    3. **Ontwikkel een visie en strategie:** De leidende coalitie definieert een heldere visie die Euro Caps positioneert als een leidende, innoverende en lerende organisatie waar kwaliteit integraal onderdeel is van ieders dagelijkse werk.

    **Fase 2: Implementatie (12 maanden)**
    Deze fase omvat Kotter's stappen 4, 5, en 6, gericht op het daadwerkelijk doorvoeren van de veranderingen.

    4. **Communiceer de veranderingsvisie:** De visie wordt breed en herhaaldelijk gecommuniceerd via diverse interne communicatiekanalen om draagvlak te creëren en te onderhouden.

    5. **Creëer draagvlak voor actie:** De leidende coalitie identificeert en verwijdert structurele obstakels die de implementatie van Six Sigma en de bredere verandering belemmeren.

    6. **Genereer korte termijn successen:** Snelle, zichtbare successen uit kleinschalige Six Sigma projecten worden gevierd en breed gecommuniceerd om motivatie te verhogen.

    **Fase 3: Verankering (6 maanden)**
    Deze finale fase omvat Kotter's stappen 7 en 8, gericht op duurzame borging.

    7. **Consolideer verbeteringen en produceer nog meer verandering:** Na de eerste successen worden de geleerde lessen uit de DMAIC-cycli systematisch gebruikt om verdere verbeteringen te initiëren.

    8. **Veranker nieuwe benaderingen in de cultuur:** De nieuwe structuren, processen en gedragingen worden diepgaand geborgd in het beleid, de prestatiebeoordeling en de interne communicatie van Euro Caps.

    [Visual van DMAIC en Kotter integratie hier. Een diagram dat toont hoe de DMAIC-cyclus geïntegreerd wordt in elk van Kotter's 8 stappen, met specifieke tijdslijnen en verantwoordelijkheden.]
    """
    
    # Find the right place to insert this content (after Kotter section)
    for i, paragraph in enumerate(doc.paragraphs):
        if "Veranderaanpak Kotter" in paragraph.text and paragraph.style.name.startswith('Heading'):
            # Insert the detailed implementation after this heading
            insert_index = i + 1
            
            # Split the content into paragraphs and add them
            content_paragraphs = kotter_implementation.strip().split('\n\n')
            for content in content_paragraphs:
                if content.strip():
                    new_para = doc.paragraphs[insert_index].insert_paragraph_before()
                    new_para.add_run(content.strip())
                    insert_index += 1
            
            break
    
    # Save the updated document
    output_path = 'Adviesrapport_Veranderingsmanagement_FINAAL_COMPLEET.docx'
    doc.save(output_path)
    print(f"Final complete document saved as: {output_path}")
    
    return output_path

if __name__ == "__main__":
    print("=== Creating Complete Document with All Visuals ===")
    
    # First, show available visuals
    print("\n1. Available visuals:")
    create_visual_summary()
    
    # Add visuals to document
    print("\n2. Adding visuals to document...")
    doc_with_visuals = add_visuals_to_document()
    
    # Add missing content
    print("\n3. Adding missing detailed content...")
    final_doc = add_missing_content_from_doc3()
    
    print(f"\n=== COMPLETED ===")
    print(f"Final document: {final_doc}")
    print("All content from document (3) has been integrated with visual indicators!")
