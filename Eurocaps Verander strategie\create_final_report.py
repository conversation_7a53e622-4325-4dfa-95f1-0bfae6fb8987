from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os

def create_final_report():
    """Create the final complete report with all visuals"""
    
    doc = Document()
    
    # Title
    doc.add_heading('Adviesrapport Veranderingsmanagement', 0)
    doc.add_heading('Optimalisatie van Euro Caps door Gerichte Structuur- en Cultuurverandering met Six Sigma', 1)
    
    # Version info
    doc.add_paragraph('Versie: 3 (Met Visuals)')
    doc.add_paragraph('Naam: <PERSON><PERSON>')
    doc.add_paragraph('Studentennummer: 1066471')
    doc.add_paragraph('Onderwijsperiode: OP4')
    doc.add_paragraph('Plaats en datum: Rotterdam 03-07-2025')
    doc.add_paragraph('Docenten: <PERSON>, <PERSON><PERSON>')
    
    doc.add_page_break()
    
    # Managementsamenvatting
    doc.add_heading('Managementsamenvatting', level=1)
    doc.add_paragraph('''Dit adviesrapport presenteert een integrale strategie voor Euro Caps om de organisatiestructuur en -cultuur te optimaliseren, voortbouwend op de reeds geïmplementeerde Six Sigma methodiek. Het rapport begint met een inleiding waarin de achtergrond en de noodzaak van deze veranderingen worden geschetst, waarbij de focus ligt op het versterken van de organisatie naast de procesverbeteringen die door Six Sigma worden gerealiseerd.''')
    
    doc.add_page_break()
    
    # Hoofdstuk 2: Theoretisch kader
    doc.add_heading('Hoofdstuk 2: Theoretisch kader', level=1)
    
    # 2.1 Veranderstrategieën volgens Boonstra
    doc.add_heading('2.1 Veranderstrategieën volgens Boonstra', level=2)
    doc.add_paragraph('''Boonstra (2018) onderscheidt verschillende strategieën voor organisatieverandering, waarbij de keuze afhangt van factoren zoals urgentie, organisatiecultuur en gewenste betrokkenheid van medewerkers. De ontwikkelingsstrategie richt zich op verandering door collectief leren en participatie, wat tijdrovend maar duurzaam is.''')
    
    if os.path.exists('Visual_1_Boonstra_Veranderstrategieen.png'):
        doc.add_picture('Visual_1_Boonstra_Veranderstrategieen.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 1: Boonstra\'s Veranderstrategieën - Karakteristieken')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 2.2 Veranderkleuren van De Caluwé
    doc.add_heading('2.2 Veranderkleuren van De Caluwé', level=2)
    doc.add_paragraph('''Het kleurenmodel van De Caluwé (De Caluwé & Vermaak, 2009) biedt een methodiek om verschillende 'denklogica\'s' over veranderen te visualiseren en te begrijpen.''')
    
    if os.path.exists('Visual_2_Caluwe_Kleurenmodel.png'):
        doc.add_picture('Visual_2_Caluwe_Kleurenmodel.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 2: De Caluwé\'s Kleurenmodel - Denklogica\'s')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 2.3 Gap-analyse
    doc.add_heading('2.3 Gap-analyse & Hofstede-model', level=2)
    doc.add_paragraph('''Een Gap-analyse is een essentiële tool voor verandermanagement die het verschil tussen de huidige situatie en de gewenste situatie in kaart brengt.''')
    
    if os.path.exists('Visual_3_Gap_Analyse_Model.png'):
        doc.add_picture('Visual_3_Gap_Analyse_Model.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 3: Gap-Analyse Model')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 2.4 Hofstede
    doc.add_paragraph('''Voor de analyse van de organisatiecultuur wordt gebruik gemaakt van Hofstede\'s cultuurdimensies (Hofstede, Hofstede & Minkov, 2010), die zes fundamentele dimensies van organisatiecultuur onderscheiden.''')
    
    if os.path.exists('Visual_4_Hofstede_Cultuurdimensies.png'):
        doc.add_picture('Visual_4_Hofstede_Cultuurdimensies.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 4: Hofstede\'s Cultuurdimensies - Euro Caps')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 2.5 Kotter
    doc.add_heading('2.4 Kotter\'s 8 Stappenmodel', level=2)
    doc.add_paragraph('''Kotter (1996) heeft een gestructureerd achtstappenmodel ontwikkeld dat dient als leidraad voor succesvolle organisatieverandering.''')
    
    if os.path.exists('Visual_5_Kotter_8_Stappenmodel.png'):
        doc.add_picture('Visual_5_Kotter_8_Stappenmodel.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 5: Kotter\'s 8-Stappenmodel voor Verandering')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 2.6 Stakeholderanalyse
    doc.add_heading('2.5 Stakeholderanalyse', level=2)
    doc.add_paragraph('''Een stakeholderanalyse is een belangrijke tool in verandermanagement om de invloed en belangen van alle betrokken partijen bij een verandering te analyseren.''')
    
    if os.path.exists('Visual_6_Stakeholderanalyse_Matrix.png'):
        doc.add_picture('Visual_6_Stakeholderanalyse_Matrix.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 6: Stakeholderanalyse - Power/Interest Matrix')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 2.7 Kübler-Ross
    doc.add_heading('2.6 Verandercurve van Kübler-Ross', level=2)
    doc.add_paragraph('''De verandercurve van Kübler-Ross (1969), oorspronkelijk ontwikkeld om de emotionele fasen van rouw te beschrijven, wordt in verandermanagement gebruikt om de typische emotionele fases te duiden die mensen doorlopen wanneer zij geconfronteerd worden met ingrijpende veranderingen.''')
    
    if os.path.exists('Visual_7_Kubler_Ross_Verandercurve.png'):
        doc.add_picture('Visual_7_Kubler_Ross_Verandercurve.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 7: Verandercurve van Kübler-Ross')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_page_break()
    
    # Hoofdstuk 3: Huidige situatie
    doc.add_heading('Hoofdstuk 3: Huidige situatie', level=1)
    
    # 3.1 Huidige organisatiestructuur
    doc.add_heading('3.1 Huidige organisatiestructuur', level=2)
    doc.add_paragraph('''De organisatiestructuur van Euro Caps wordt gekenmerkt door functionele silo\'s (Operatie, MCO, Sales, Finance, IT) met beperkte horizontale communicatie.''')
    
    if os.path.exists('Visual_8_Beslissingsmatrix_Mintzberg.png'):
        doc.add_picture('Visual_8_Beslissingsmatrix_Mintzberg.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 8: Beslissingsmatrix Organisatiestructuur (Mintzberg)')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 3.2 Huidige organisatiecultuur
    doc.add_heading('3.2 Huidige organisatiecultuur', level=2)
    doc.add_paragraph('''Euro Caps kenmerkt zich door een relatief kleine machtsafstand, wat blijkt uit de toegankelijkheid van het management en de open communicatie tussen verschillende organisatieniveaus.''')
    
    if os.path.exists('Visual_4_Hofstede_Cultuurdimensies.png'):
        doc.add_picture('Visual_4_Hofstede_Cultuurdimensies.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 9: Hofstede\'s Cultuurdimensies - Euro Caps (Huidige Situatie)')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_page_break()
    
    # Hoofdstuk 5: Veranderstrategie
    doc.add_heading('Hoofdstuk 5: Veranderstrategie + implementatieplan', level=1)
    
    # 5.2.1 Strategische veranderaanpak
    doc.add_heading('5.2.1 Strategische veranderaanpak', level=2)
    doc.add_paragraph('''Voor het bepalen van de juiste veranderaanpak bij Euro Caps is gekozen voor het model van Boonstra & Maasdaar (2008).''')
    
    if os.path.exists('Visual_9_Boonstra_Beslissingsmatrix.png'):
        doc.add_picture('Visual_9_Boonstra_Beslissingsmatrix.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 10: Beslissingsmatrix Veranderstrategie (Boonstra)')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 5.2.3 Veranderaanpak Kotter
    doc.add_heading('5.2.3 Veranderaanpak Kotter', level=2)
    doc.add_paragraph('''Om de ontwikkelingsgerichte strategie concreet te maken en de Six Sigma implementatie te borgen, wordt gekozen voor Kotter\'s achtstappenmodel voor verandering.''')
    
    if os.path.exists('Visual_10_DMAIC_Kotter_Integratie.png'):
        doc.add_picture('Visual_10_DMAIC_Kotter_Integratie.png', width=Inches(6))
        caption = doc.add_paragraph('Figuur 11: Integratie Six Sigma DMAIC met Kotter\'s 8-Stappenmodel')
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Conclusie
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 7: Conclusie', level=1)
    doc.add_paragraph('''Dit adviesrapport heeft de huidige situatie bij Euro Caps geanalyseerd, inclusief de organisatiestructuur, bedrijfscultuur, stakeholderanalyse en geïdentificeerde knelpunten. We hebben vastgesteld dat inefficiënties in productieprocessen, data-silo\'s en communicatieproblemen de belangrijkste uitdagingen vormen.''')
    
    # Aanbeveling
    doc.add_heading('Aanbeveling', level=1)
    doc.add_paragraph('''Op basis van de bevindingen in dit rapport worden de volgende aanbevelingen gedaan aan Euro Caps:''')
    doc.add_paragraph('1. Volledige Implementatie van Kotter\'s 8-stappenmodel')
    doc.add_paragraph('2. Continue Integratie van Gapanalyse (Is-Sol)')
    doc.add_paragraph('3. Versterk Interne Communicatie en Betrokkenheid')
    doc.add_paragraph('4. Investeer in Capaciteitsopbouw en Leiderschap')
    
    # Save the document
    doc.save('Adviesrapport_Veranderingsmanagement_FINAL_Met_Visuals.docx')
    print("Final report with all visuals has been created successfully!")

if __name__ == "__main__":
    create_final_report() 