import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
import os

def generate_stakeholder_matrix():
    fig, ax = plt.subplots(figsize=(7, 4))
    ax.axis('off')
    table_data = [
        ["", "<PERSON>ag", "<PERSON><PERSON>", "Hoog", "Zeer Hoog"],
        ["Zeer hoog", "Beïnvloeder\n(tevrede houden)", "", "Belangrijke speler\n(vertroetelen)", ""],
        ["Hoog", "", "", "", ""],
        ["Matig", "Toeschouwer\n(weinig aandacht)", "", "<PERSON><PERSON>hebbende\n(op de hoogte houden)", ""],
        ["Laag", "", "", "", ""]
    ]
    table = ax.table(cellText=table_data, loc='center', cellLoc='center', colWidths=[0.15]*5)
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 2)
    plt.title('Stakeholdermatrix (Invloed vs. Belang)', fontsize=14)
    plt.savefig('Visual_Stakeholdermatrix.png', bbox_inches='tight')
    plt.close()

def generate_kotter_steps():
    steps = [
        "1. Urgentiebesef creëren",
        "2. Leiderschapscoalitie vormen",
        "3. Visie en strategie ontwikkelen",
        "4. Communiceer de visie",
        "5. Obstakels wegnemen",
        "6. Korte termijn successen behalen",
        "7. Consolideer en versnel",
        "8. Veranker de verandering"
    ]
    fig, ax = plt.subplots(figsize=(7, 4))
    ax.axis('off')
    for i, step in enumerate(steps):
        ax.text(0.1, 1 - i*0.12, step, fontsize=12, va='center')
    plt.title("Kotter's 8-stappenmodel", fontsize=14)
    plt.savefig('Visual_Kotter8Stappen.png', bbox_inches='tight')
    plt.close()

def generate_stakeholder_phases():
    fig, ax = plt.subplots(figsize=(7, 3))
    ax.axis('off')
    phases = ["Inform", "Consult", "Collaborate"]
    y = [0.8, 0.5, 0.2]
    for i, phase in enumerate(phases):
        ax.text(0.1, y[i], phase, fontsize=12, weight='bold')
    ax.text(0.3, 0.8, "In contact komen met stakeholders en hun betrokkenheid onderzoeken.", fontsize=10)
    ax.text(0.3, 0.5, "Beïnvloeden van key stakeholders door conflicterende belangen te bespreken.", fontsize=10)
    ax.text(0.3, 0.2, "Alle beïnvloeding weer teruggeven aan de relevante stakeholders.", fontsize=10)
    plt.title('Stakeholdermanagement fasen', fontsize=14)
    plt.savefig('Visual_Stakeholdermanagementfasen.png', bbox_inches='tight')
    plt.close()

def generate_stakeholder_map():
    fig, ax = plt.subplots(figsize=(5, 5))
    ax.axis('off')
    circles = [1, 2, 3, 4]
    labels = ['Kernteam', 'Medewerkers', 'Klanten', 'Externe partijen']
    for i, r in enumerate(circles[::-1]):
        circle = plt.Circle((0.5, 0.5), r*0.12, color='C0', fill=False, lw=2)
        ax.add_artist(circle)
        ax.text(0.5, 0.5 + r*0.12, labels[::-1][i], ha='center', va='center', fontsize=10)
    plt.title('Stakeholdermap Euro Caps', fontsize=14)
    plt.savefig('Visual_Stakeholdermap.png', bbox_inches='tight')
    plt.close()

def generate_all_visuals():
    generate_stakeholder_matrix()
    generate_kotter_steps()
    generate_stakeholder_phases()
    generate_stakeholder_map()
    print("Alle visuals gegenereerd.")

if __name__ == "__main__":
    generate_all_visuals() 