import docx
from docx.shared import Pt
from docx.oxml.ns import qn
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

# Document aanmaken
doc = docx.Document()

# Titel
heading = doc.add_heading('Stakeholdersanalyse', 0)
heading.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

# Inleiding
intro = (
    'Voor een succesvolle implementatie is het essentieel om een helder beeld te hebben van de betrokken stakeholders. '
    'De onderstaande tabellen bieden een overzicht van de belangrijkste stakeholders, hun rol, mate van betrokken<PERSON>id, belang, positie en invloed, '
    'alsook een categorisatie voor een goede uitvoering van het implementatieadvies.'
)
doc.add_paragraph(intro)

doc.add_paragraph('')

# Tabel 2: Overzicht stakeholders
stakeholders = [
    ['Bedrijfsvoering', '<PERSON><PERSON><PERSON>', 'Manager Bedrijfsvoering', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
    ['<PERSON>le<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
    ['<PERSON><PERSON>', '<PERSON><PERSON>', 'CEO', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
    ['I<PERSON>', '<PERSON> Dekker', 'Manager ICT', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
    ['Financiën', 'Berkan Arrindell', 'Hoofd <PERSON>anciën', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
    ['Productie', 'Ismail Berenschot', 'Productiemedewerker', 'Gemiddeld', 'Gemiddeld', 'Blocker', 'Laag'],
    ['Productie', 'Samantha Mukhlis Aswad', 'Productiemedewerker', 'Gemiddeld', 'Gemiddeld', 'Blocker', 'Laag'],
    ['Logistiek', 'Rijk Wegen', 'Manager Logistiek', 'Gemiddeld', 'Hoog', 'Floater', 'Hoog'],
    ['Inkoop', 'Ko Jager', 'Manager Inkoop', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
    ['Logistiek', 'Tila Karren', 'Medewerker Logistiek (Inkomend/Uitgaand)', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
    ['Kwaliteitsbeheer', 'Kees Keurig', 'Hoofd Kwaliteitsbeheer / Team Kwaliteitswaarborging', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
]

doc.add_paragraph('Tabel 2: Overzicht stakeholders')
table = doc.add_table(rows=1, cols=7)
hdr_cells = table.rows[0].cells
headers = ['Stakeholdergroep', 'Naam stakeholder', 'Functie', 'Betrokkenheid', 'Belang', 'Positie', 'Invloed']
for i, h in enumerate(headers):
    hdr_cells[i].text = h
for row in stakeholders:
    row_cells = table.add_row().cells
    for i, item in enumerate(row):
        row_cells[i].text = item

doc.add_paragraph('')

# Tabel 3: Categorisatie van de stakeholders
doc.add_paragraph('Tabel 3: Categorisatie van de stakeholders')

table2 = doc.add_table(rows=4, cols=3)
table2.style = 'Table Grid'
table2.cell(0, 0).text = 'Soort stakeholder'
table2.cell(0, 1).text = 'Primair (directe invloed)'
table2.cell(0, 2).text = 'Secundair (indirecte invloed)'
table2.cell(1, 0).text = 'Interne stakeholders'
table2.cell(1, 1).text = '- Manager ICT (Dirk Dekker)\n- Projectleider (Niene Tepe)\n- Manager Bedrijfsvoering (Sarel Bosland)'
table2.cell(1, 2).text = '- Manager Inkoop (Kia Jager)\n- Manager Logistiek (Rick Ingman)\n- Productiemanagers (Nino Taper, Maik Ritter, Maria Stanić)\n- CEO (Nia Clement)\n- HR-manager (Ilona Laman)\n- Ondernemingsraad (OR)\n- Afdeling Kwaliteitsbeheer (Kees Keurig)'
table2.cell(2, 0).text = 'Externe stakeholders'
table2.cell(2, 1).text = '- Leveranciers (specifieke Six Sigma partners, machines, etc.)'
table2.cell(2, 2).text = '- Overheid / NVWA (voedselveiligheid)\n- Certificeringsinstanties (UTZ, Organic, Fair Trade)'
table2.cell(3, 0).text = 'Interface stakeholders'
table2.cell(3, 1).text = '–'
table2.cell(3, 2).text = '- Klanten (retailers, koffiebranders)'

doc.add_paragraph('')

# Uitleg positie-categorieën
uitleg = (
    'De positie-categorie (Mover, Blocker, Floater) geeft inzicht in hoe elke stakeholder zich opstelt ten opzichte van het verandertraject: '
    'een Mover is actief betrokken, denkt mee en stimuleert de verandering; een Blocker is kritisch of terughoudend en kan de verandering belemmeren; '
    'een Floater is neutraal en beweegt mee met de situatie. Deze classificatie helpt bij het bepalen van de juiste communicatie- en interventiestrategie.'
)
doc.add_paragraph(uitleg)

# Opslaan
doc.save('Eurocaps Verander strategie/Stakeholderanalyse_Eurocaps.docx') 