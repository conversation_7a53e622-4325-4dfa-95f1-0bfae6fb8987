import os
from docx import Document

def read_word_document(file_path):
    """Read content from a Word document"""
    try:
        doc = Document(file_path)
        content = []
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content.append(paragraph.text.strip())
        
        return '\n'.join(content)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

# Read both CV documents
cv_folder = r"C:\Users\<USER>\Documents\augment-projects\Americaps\cv-update"

# Read the old CV
old_cv_path = os.path.join(cv_folder, "CV Shuja 2025-06-09-25_update.docx")
old_cv_content = read_word_document(old_cv_path)

# Read the modern CV template
modern_cv_path = os.path.join(cv_folder, "CV Shuja Schadon - Modern 2025.docx")
modern_cv_content = read_word_document(modern_cv_path)

# Save content to text files
if old_cv_content:
    with open(os.path.join(cv_folder, "old_cv_content.txt"), 'w', encoding='utf-8') as f:
        f.write(old_cv_content)
    print("Old CV content saved to old_cv_content.txt")

if modern_cv_content:
    with open(os.path.join(cv_folder, "modern_cv_content.txt"), 'w', encoding='utf-8') as f:
        f.write(modern_cv_content)
    print("Modern CV content saved to modern_cv_content.txt")

print("\nOld CV Content Preview:")
print("=" * 50)
if old_cv_content:
    print(old_cv_content[:1000] + "..." if len(old_cv_content) > 1000 else old_cv_content)

print("\nModern CV Content Preview:")
print("=" * 50)
if modern_cv_content:
    print(modern_cv_content[:1000] + "..." if len(modern_cv_content) > 1000 else modern_cv_content)
