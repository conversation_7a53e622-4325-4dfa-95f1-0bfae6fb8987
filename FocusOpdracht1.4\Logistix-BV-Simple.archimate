<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix B.V. - Huidige Architectuur" id="id-logistix-model-123" version="5.0.0">
  <folder name="Strategy" id="id-strategy-123" type="strategy"/>
  <folder name="Business" id="id-business-123" type="business">
    <element xsi:type="archimate:BusinessActor" name="Grote Klanten" id="id-grote-klanten"/>
    <element xsi:type="archimate:BusinessActor" name="Kleine Klanten" id="id-kleine-klanten"/>
    <element xsi:type="archimate:BusinessActor" name="Medewerker" id="id-medewerker"/>
    <element xsi:type="archimate:BusinessActor" name="Klantenservice" id="id-klantenservice"/>
    
    <element xsi:type="archimate:BusinessProcess" name="Orderverwerking" id="id-orderverwerking"/>
    <element xsi:type="archimate:BusinessProcess" name="Order Ontvangen" id="id-order-ontvangen"/>
    <element xsi:type="archimate:BusinessProcess" name="Handmatige Invoer ERP" id="id-handmatige-erp"/>
    <element xsi:type="archimate:BusinessProcess" name="Handmatige Invoer WMS" id="id-handmatige-wms"/>
    <element xsi:type="archimate:BusinessProcess" name="Magazijnbeheer" id="id-magazijnbeheer"/>
    <element xsi:type="archimate:BusinessProcess" name="Picking en Packing" id="id-picking-packing"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzending" id="id-verzending"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantenservice Proces" id="id-klantenservice-proces"/>
    
    <element xsi:type="archimate:BusinessObject" name="Order" id="id-order"/>
    <element xsi:type="archimate:BusinessObject" name="Klantgegevens" id="id-klantgegevens"/>
    <element xsi:type="archimate:BusinessObject" name="Verzendlabel" id="id-verzendlabel"/>
    
    <element xsi:type="archimate:BusinessService" name="E-fulfilment Service" id="id-efulfilment"/>
    <element xsi:type="archimate:BusinessService" name="Logistieke Dienstverlening" id="id-logistiek"/>
  </folder>
  <folder name="Application" id="id-application-123" type="application">
    <element xsi:type="archimate:ApplicationComponent" name="ERP Systeem" id="id-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS Systeem" id="id-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS Systeem" id="id-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM Systeem" id="id-crm"/>
    
    <element xsi:type="archimate:ApplicationService" name="Klantbeheer" id="id-klantbeheer"/>
    <element xsi:type="archimate:ApplicationService" name="Magazijnbeheer Service" id="id-magazijn-service"/>
    <element xsi:type="archimate:ApplicationService" name="Transport Service" id="id-transport-service"/>
    <element xsi:type="archimate:ApplicationService" name="CRM Service" id="id-crm-service"/>
    
    <element xsi:type="archimate:DataObject" name="Orderdata" id="id-orderdata"/>
    <element xsi:type="archimate:DataObject" name="Klantdata" id="id-klantdata"/>
    <element xsi:type="archimate:DataObject" name="Voorraaddata" id="id-voorraaddata"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-123" type="technology">
    <element xsi:type="archimate:Node" name="On-premise Server" id="id-onpremise"/>
    <element xsi:type="archimate:Node" name="Cloud Platform" id="id-cloud"/>
    <element xsi:type="archimate:Node" name="Standalone Server" id="id-standalone"/>
    
    <element xsi:type="archimate:CommunicationNetwork" name="Bedrijfsnetwerk" id="id-netwerk"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Internet" id="id-internet"/>
    
    <element xsi:type="archimate:Device" name="Werkstation" id="id-werkstation"/>
  </folder>
  <folder name="Motivation" id="id-motivation-123" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-123" type="implementation_migration"/>
  <folder name="Other" id="id-other-123" type="other"/>
  <folder name="Relations" id="id-relations-123" type="relations">
    <element xsi:type="archimate:TriggeringRelationship" id="rel-klanten-order" source="id-grote-klanten" target="id-order-ontvangen"/>
    <element xsi:type="archimate:TriggeringRelationship" id="rel-kleine-klanten-order" source="id-kleine-klanten" target="id-order-ontvangen"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-medewerker-order" source="id-medewerker" target="id-orderverwerking"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-medewerker-magazijn" source="id-medewerker" target="id-magazijnbeheer"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-klantenservice-proces" source="id-klantenservice" target="id-klantenservice-proces"/>
    
    <element xsi:type="archimate:CompositionRelationship" id="rel-order-ontvangen" source="id-orderverwerking" target="id-order-ontvangen"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-order-erp" source="id-orderverwerking" target="id-handmatige-erp"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-order-wms" source="id-orderverwerking" target="id-handmatige-wms"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-picking" source="id-magazijnbeheer" target="id-picking-packing"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-verzending" source="id-magazijnbeheer" target="id-verzending"/>
    
    <element xsi:type="archimate:FlowRelationship" id="rel-ontvangen-erp" source="id-order-ontvangen" target="id-handmatige-erp"/>
    <element xsi:type="archimate:FlowRelationship" id="rel-ontvangen-wms" source="id-order-ontvangen" target="id-handmatige-wms"/>
    <element xsi:type="archimate:FlowRelationship" id="rel-picking-verzending" source="id-picking-packing" target="id-verzending"/>
    
    <element xsi:type="archimate:RealizationRelationship" id="rel-erp-klantbeheer" source="id-erp" target="id-klantbeheer"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-wms-magazijn" source="id-wms" target="id-magazijn-service"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-tms-transport" source="id-tms" target="id-transport-service"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-crm-service" source="id-crm" target="id-crm-service"/>
    
    <element xsi:type="archimate:ServingRelationship" id="rel-klantbeheer-erp" source="id-klantbeheer" target="id-handmatige-erp"/>
    <element xsi:type="archimate:ServingRelationship" id="rel-magazijn-picking" source="id-magazijn-service" target="id-picking-packing"/>
    <element xsi:type="archimate:ServingRelationship" id="rel-transport-verzending" source="id-transport-service" target="id-verzending"/>
    <element xsi:type="archimate:ServingRelationship" id="rel-crm-klantenservice" source="id-crm-service" target="id-klantenservice-proces"/>
    
    <element xsi:type="archimate:AccessRelationship" id="rel-erp-orderdata" source="id-erp" target="id-orderdata"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-erp-klantdata" source="id-erp" target="id-klantdata"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-wms-voorraaddata" source="id-wms" target="id-voorraaddata"/>
    
    <element xsi:type="archimate:RealizationRelationship" id="rel-cloud-erp" source="id-cloud" target="id-erp"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-onpremise-wms" source="id-onpremise" target="id-wms"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-standalone-tms" source="id-standalone" target="id-tms"/>
    
    <element xsi:type="archimate:AssociationRelationship" id="rel-netwerk-cloud" source="id-netwerk" target="id-cloud"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-netwerk-onpremise" source="id-netwerk" target="id-onpremise"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-netwerk-standalone" source="id-netwerk" target="id-standalone"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-internet-cloud" source="id-internet" target="id-cloud"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-netwerk-internet" source="id-netwerk" target="id-internet"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-werkstation-netwerk" source="id-werkstation" target="id-netwerk"/>
  </folder>
  <folder name="Views" id="id-views-123" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix B.V. Overzicht" id="id-main-view">
      <child xsi:type="archimate:DiagramObject" id="diag-grote-klanten" archimateElement="id-grote-klanten">
        <bounds x="50" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-grote-order" source="diag-grote-klanten" target="diag-order-ontvangen" archimateRelationship="rel-klanten-order"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-kleine-klanten" archimateElement="id-kleine-klanten">
        <bounds x="200" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-kleine-order" source="diag-kleine-klanten" target="diag-order-ontvangen" archimateRelationship="rel-kleine-klanten-order"/>
      </child>
      
      <child xsi:type="archimate:DiagramObject" id="diag-orderverwerking" archimateElement="id-orderverwerking">
        <bounds x="50" y="150" width="500" height="150"/>
        <child xsi:type="archimate:DiagramObject" id="diag-order-ontvangen" archimateElement="id-order-ontvangen">
          <bounds x="20" y="30" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-ontvangen-erp" source="diag-order-ontvangen" target="diag-handmatige-erp" archimateRelationship="rel-ontvangen-erp"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-ontvangen-wms" source="diag-order-ontvangen" target="diag-handmatige-wms" archimateRelationship="rel-ontvangen-wms"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-handmatige-erp" archimateElement="id-handmatige-erp">
          <bounds x="170" y="30" width="120" height="55"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-handmatige-wms" archimateElement="id-handmatige-wms">
          <bounds x="320" y="30" width="120" height="55"/>
        </child>
      </child>
      
      <child xsi:type="archimate:DiagramObject" id="diag-magazijnbeheer" archimateElement="id-magazijnbeheer">
        <bounds x="50" y="350" width="500" height="100"/>
        <child xsi:type="archimate:DiagramObject" id="diag-picking-packing" archimateElement="id-picking-packing">
          <bounds x="20" y="30" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-picking-verzending" source="diag-picking-packing" target="diag-verzending" archimateRelationship="rel-picking-verzending"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-verzending" archimateElement="id-verzending">
          <bounds x="170" y="30" width="120" height="55"/>
        </child>
      </child>
      
      <child xsi:type="archimate:DiagramObject" id="diag-klantenservice-proces" archimateElement="id-klantenservice-proces">
        <bounds x="600" y="150" width="150" height="100"/>
      </child>
      
      <child xsi:type="archimate:DiagramObject" id="diag-erp" archimateElement="id-erp">
        <bounds x="100" y="500" width="120" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-erp-handmatige" source="diag-erp" target="diag-handmatige-erp" archimateRelationship="rel-klantbeheer-erp"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-wms" archimateElement="id-wms">
        <bounds x="250" y="500" width="120" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-wms-picking" source="diag-wms" target="diag-picking-packing" archimateRelationship="rel-magazijn-picking"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-tms" archimateElement="id-tms">
        <bounds x="400" y="500" width="120" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-tms-verzending" source="diag-tms" target="diag-verzending" archimateRelationship="rel-transport-verzending"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-crm" archimateElement="id-crm">
        <bounds x="600" y="500" width="120" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-crm-klantenservice" source="diag-crm" target="diag-klantenservice-proces" archimateRelationship="rel-crm-klantenservice"/>
      </child>
      
      <child xsi:type="archimate:DiagramObject" id="diag-cloud" archimateElement="id-cloud">
        <bounds x="100" y="600" width="120" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-cloud-erp" source="diag-cloud" target="diag-erp" archimateRelationship="rel-cloud-erp"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-onpremise" archimateElement="id-onpremise">
        <bounds x="250" y="600" width="120" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-onpremise-wms" source="diag-onpremise" target="diag-wms" archimateRelationship="rel-onpremise-wms"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-standalone" archimateElement="id-standalone">
        <bounds x="400" y="600" width="120" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-standalone-tms" source="diag-standalone" target="diag-tms" archimateRelationship="rel-standalone-tms"/>
      </child>
      
      <child xsi:type="archimate:DiagramObject" id="diag-netwerk" archimateElement="id-netwerk">
        <bounds x="200" y="700" width="400" height="40"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-netwerk-cloud" source="diag-netwerk" target="diag-cloud" archimateRelationship="rel-netwerk-cloud"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-netwerk-onpremise" source="diag-netwerk" target="diag-onpremise" archimateRelationship="rel-netwerk-onpremise"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-netwerk-standalone" source="diag-netwerk" target="diag-standalone" archimateRelationship="rel-netwerk-standalone"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-netwerk-internet" source="diag-netwerk" target="diag-internet" archimateRelationship="rel-netwerk-internet"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-internet" archimateElement="id-internet">
        <bounds x="50" y="700" width="120" height="40"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-internet-cloud" source="diag-internet" target="diag-cloud" archimateRelationship="rel-internet-cloud"/>
      </child>
    </element>
  </folder>
</archimate:model>
