from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import Pt, RGBColor

SRC = 'cv-update/CV Shuja 2025-06-09-25_update.docx'
DEST = 'cv-update/CV Shuja Schadon - Modern 2025.docx'

HEADING_MAP = {
    'wie ben ik': 'PROFIEL',
    'praktische': 'VAARDIGHEDEN',
    'opleidingen': 'OPLEIDING',
    'werkervaring': 'WERKERVARING',
    'cyber security cursussen': 'CURSUSSEN (LOPEND)',
    'auto lessen': None,  # verwijderen
    'autolessen': None,
    'kennis van talen': None,
    'talen': None,
}

DROP_SECTIONS = {k for k, v in HEADING_MAP.items() if v is None}


def norm(text: str) -> str:
    return (text or '').strip().lower()


def extract_sections(src_path: str):
    doc = Document(src_path)
    current = 'MISC'
    sections = { 'MISC': [], 'PROFIEL': [], 'VAAR<PERSON>GHEDEN': [], 'WERKERVARING': [], 'OPLEIDING': [], 'CURSUSSEN (LOPEND)': [] }

    for p in doc.paragraphs:
        t = p.text.strip()
        if not t:
            continue
        key = norm(t)
        if key in HEADING_MAP:
            mapped = HEADING_MAP[key]
            if mapped is None:
                current = None  # we skip until next heading
            else:
                current = mapped
            continue
        # if paragraph style name looks like a heading, try match quickly
        if p.style and p.style.name and 'Heading' in p.style.name:
            # leave current unchanged unless the text matches something known
            pass
        if current is None:
            # skipping dropped section content
            continue
        sections[current].append(t)
    return sections


def add_heading(doc: Document, text: str):
    run = doc.add_paragraph().add_run(text)
    run.bold = True
    font = run.font
    font.size = Pt(14)
    font.color.rgb = RGBColor(33, 37, 41)  # dark gray


def add_bullets(doc: Document, lines):
    for line in lines:
        if not line:
            continue
        para = doc.add_paragraph(line, style='List Bullet')
        para_format = para.paragraph_format
        para_format.space_after = Pt(2)


def build_doc(sections):
    doc = Document()
    # Base font
    style = doc.styles['Normal']
    style.font.name = 'Arial'
    style.font.size = Pt(11)

    # Title
    h = doc.add_heading('Shuja Schadon', level=0)
    h.alignment = WD_ALIGN_PARAGRAPH.LEFT

    contact = doc.add_paragraph('Rotterdam | 06-28411923 | <EMAIL>')
    contact.alignment = WD_ALIGN_PARAGRAPH.LEFT

    # Profiel: pak uit PROFIEL of genereer korte samenvatting
    add_heading(doc, 'Profiel')
    if sections['PROFIEL']:
        for line in sections['PROFIEL']:
            doc.add_paragraph(line)
    else:
        doc.add_paragraph(
            'Praktisch ingestelde en gemotiveerde IT-professional met ervaring in '
            'installatie en ondersteuning van hardware, besturingssystemen en basisnetwerken.'
        )

    # Vaardigheden: neem uit VAARDIGHEDEN of laat leeg indien niet aanwezig
    if sections['VAARDIGHEDEN']:
        add_heading(doc, 'Vaardigheden')
        add_bullets(doc, sections['VAARDIGHEDEN'])

    # Werkervaring: behoud alle alinea's zoals aanwezig
    if sections['WERKERVARING']:
        add_heading(doc, 'Werkervaring')
        for line in sections['WERKERVARING']:
            if line.endswith(':'):
                # vermoedelijk subtitel -> vet
                run = doc.add_paragraph().add_run(line[:-1])
                run.bold = True
            else:
                doc.add_paragraph(line)

    # Opleiding
    if sections['OPLEIDING']:
        add_heading(doc, 'Opleiding')
        for line in sections['OPLEIDING']:
            doc.add_paragraph(line)

    # Cursussen
    if sections['CURSUSSEN (LOPEND)']:
        add_heading(doc, 'Cursussen (lopend)')
        add_bullets(doc, sections['CURSUSSEN (LOPEND)'])

    # Persoonsgegevens
    add_heading(doc, 'Persoonsgegevens')
    for line in [
        'Naam: Shuja Schadon',
        'Adres: Schinnenbaan 2, 3077 JJ Rotterdam',
        'Geboortedatum: 11 april 1998',
        'Nationaliteit: Nederlands',
        'Telefoon: 06-28411923 | 010-4113222',
        'E-mail: <EMAIL>',
    ]:
        doc.add_paragraph(line)

    return doc


def main():
    sections = extract_sections(SRC)
    doc = build_doc(sections)
    doc.save(DEST)


if __name__ == '__main__':
    main()

