import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor

def create_proper_table(doc, title, headers, data):
    """Create a properly formatted table"""
    
    # Add table title
    table_title = doc.add_paragraph()
    table_title_run = table_title.add_run(title)
    table_title_run.font.bold = True
    table_title_run.font.size = Pt(12)
    table_title_run.font.name = 'Arial'
    
    # Create table
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Add headers
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        # Make header bold
        for paragraph in hdr_cells[i].paragraphs:
            for run in paragraph.runs:
                run.font.bold = True
                run.font.size = Pt(11)
                run.font.name = 'Arial'
    
    # Add data rows
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
            # Set font size for data cells
            for paragraph in row_cells[i].paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(10)
                    run.font.name = 'Arial'
    
    doc.add_paragraph()  # Add space after table
    return table

def add_visual_with_proper_caption(doc, visual_file, caption_text):
    """Add visual with proper caption and formatting"""
    
    if os.path.exists(visual_file):
        try:
            # Add the image
            paragraph = doc.add_paragraph()
            run = paragraph.add_run()
            run.add_picture(visual_file, width=Inches(6))
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add caption
            caption_paragraph = doc.add_paragraph()
            caption_run = caption_paragraph.add_run(caption_text)
            caption_run.font.size = Pt(10)
            caption_run.font.italic = True
            caption_run.font.name = 'Arial'
            caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            doc.add_paragraph()  # Add space after visual
            print(f"Added visual: {visual_file}")
            return True
            
        except Exception as e:
            print(f"Error adding visual {visual_file}: {str(e)}")
            return False
    else:
        print(f"Visual file not found: {visual_file}")
        return False

def add_chapter_5_complete(doc):
    """Add complete Chapter 5 with all subsections"""
    
    # Chapter 5: Change Strategy and Implementation Plan
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 5: Veranderstrategie + implementatieplan', 1)
    
    intro_ch5 = """Dit hoofdstuk vormt de kern van het adviesrapport, aangezien hier de specifieke veranderstrategie voor Euro Caps wordt uiteengezet en een concreet implementatieplan wordt gepresenteerd. Het begint met een gedetailleerd voorbereidende fase, die de noodzakelijke structurele en culturele veranderingen identificeert en de betrokken stakeholders analyseert op mogelijke weerstanden."""
    doc.add_paragraph(intro_ch5)
    
    # 5.1 Preparatory phase
    doc.add_heading('5.1 Voorbereidende deel', 2)
    
    # 5.1.1 Organizational structure changes
    doc.add_heading('5.1.1 Organisatiestructuur veranderingen', 2)
    structure_changes = """De structurele veranderingen richten zich op het faciliteren van meer wederzijdse aanpassing en standaardisatie van vaardigheden binnen Euro Caps, ter ondersteuning van de Six Sigma aanpak (Mintzberg, 1983). Dit betekent het implementeren van meer multidisciplinaire teams, vooral rondom specifieke Six Sigma projecten (DMAIC-cycli), waarin medewerkers van verschillende afdelingen met diverse expertise samenwerken aan procesverbeteringen."""
    doc.add_paragraph(structure_changes)
    
    # 5.1.2 Organizational culture changes
    doc.add_heading('5.1.2 Organisatiecultuur veranderingen', 2)
    culture_changes = """De culturele veranderingen zijn gericht op het cultiveren van een mensgerichte en lerende organisatiecultuur, naast de bestaande focus op kwaliteit en resultaten (Hofstede, Hofstede & Minkov, 2010). Dit vereist het proactief stimuleren van feedback en dialoog, zowel top-down als bottom-up."""
    doc.add_paragraph(culture_changes)
    
    # 5.1.3 Stakeholder analysis - CORRECTED for Euro Caps
    doc.add_heading('5.1.3 Stakeholdersanalyse', 2)
    
    stakeholder_intro = """Voor een succesvolle implementatie van Six Sigma bij Euro Caps is het essentieel om een helder beeld te hebben van alle betrokken stakeholders. De onderstaande analyse identificeert de werkelijke stakeholders van Euro Caps op basis van de organisatiestructuur en externe relaties, inclusief klanten als cruciale externe stakeholder."""
    doc.add_paragraph(stakeholder_intro)
    
    # Euro Caps stakeholder data - CORRECTED
    stakeholder_headers = ['Stakeholder', 'Type', 'Belang', 'Invloed', 'Positie', 'Strategie']
    stakeholder_data = [
        ['CEO (Nils Clement)', 'Intern-Primair', 'Hoog', 'Hoog', 'Mover', 'Betrekken bij visie'],
        ['Manager Bedrijfsvoering (Servé Bosland)', 'Intern-Primair', 'Hoog', 'Hoog', 'Mover', 'Actief betrekken'],
        ['Manager ICT (Erik Dekker)', 'Intern-Primair', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Informeren en ondersteunen'],
        ['Productiemanager (Maik Ritter)', 'Intern-Primair', 'Hoog', 'Hoog', 'Blocker', 'Overtuigen en trainen'],
        ['Productiemanager (Maria Stanić)', 'Intern-Primair', 'Hoog', 'Hoog', 'Blocker', 'Overtuigen en trainen'],
        ['Hoofd Kwaliteitsbeheer (Kees Keurig)', 'Intern-Primair', 'Hoog', 'Gemiddeld', 'Mover', 'Actief betrekken'],
        ['HR Manager (Uwe Regel)', 'Intern-Secundair', 'Gemiddeld', 'Laag', 'Floater', 'Ondersteuning vragen'],
        ['Hoofd Financiën (Berkan Arrindell)', 'Intern-Secundair', 'Hoog', 'Gemiddeld', 'Floater', 'Rapporteren ROI'],
        ['Manager Logistiek (Rijk Wegen)', 'Intern-Secundair', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Informeren'],
        ['Manager Inkoop (Ko Jager)', 'Intern-Secundair', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Informeren'],
        ['Productiemedewerkers (Ismail, Samantha)', 'Intern-Primair', 'Gemiddeld', 'Laag', 'Blocker', 'Informeren en betrekken'],
        ['Klanten (Retailers/Koffiebranders)', 'Extern-Primair', 'Hoog', 'Hoog', 'Mover', 'Communiceren voordelen'],
        ['Leveranciers (Machines/Grondstoffen)', 'Extern-Secundair', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Informeren over eisen'],
        ['NVWA (Toezichthouder)', 'Extern-Secundair', 'Hoog', 'Hoog', 'Floater', 'Compliance waarborgen'],
        ['Certificeringsinstanties (UTZ/Organic)', 'Extern-Secundair', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Standaarden naleven']
    ]
    
    # Create stakeholder table
    create_proper_table(doc, 'Tabel 5.1: Stakeholderanalyse Euro Caps', stakeholder_headers, stakeholder_data)
    
    # 5.1.4 Kübler-Ross resistance analysis
    doc.add_heading('5.1.4 Mogelijke weerstanden van Kübler-Ross', 2)
    resistance_text = """Mogelijke weerstanden kunnen zich bij de verschillende stakeholdergroepen manifesteren in diverse vormen, zoals beschreven in de fasen van rouw van Kübler-Ross (1969). Bij de Directie (Nils Clement, Servé Bosland, Berkan Arrindell) kan in eerste instantie ontkenning van de noodzaak tot diepgaande cultuurverandering optreden. Het Middenkader (Erik Dekker, Maik Ritter, Maria Stanić, Rijk Wegen, Ko Jager, Kees Keurig, Uwe Regel) kan woede en frustratie ervaren over de verschuiving in hun rol en verantwoordelijkheden. Productiemedewerkers (Ismail Berenschot, Samantha Mukhlis Aswad) kunnen een gevoel van angst of zelfs depressie ervaren door de veranderingen in werkprocessen."""
    doc.add_paragraph(resistance_text)
    
    # 5.2 Implementation phase
    doc.add_heading('5.2 Uitvoerende deel', 2)
    
    # 5.2.1 Strategic change approach
    doc.add_heading('5.2.1 Strategische veranderaanpak', 2)
    strategic_approach = """Voor het bepalen van de juiste veranderaanpak bij Euro Caps is gekozen voor het model van Boonstra (2018). De volgende beslissingsmatrix illustreert de afweging tussen verschillende veranderstrategieën op basis van hun geschiktheid voor de cultuur en structuur van Euro Caps."""
    doc.add_paragraph(strategic_approach)
    
    # Boonstra decision matrix
    boonstra_headers = ['Strategie', 'Past bij cultuur', 'Past bij structuur', 'Weinig weerstand', 'Sterke samenwerking', 'Langetermijn effect', 'Totale score']
    boonstra_data = [
        ['Ontwikkelingsstrategie', '4', '4', '4', '5', '5', '22'],
        ['Ingrijpende strategie', '2', '3', '2', '3', '4', '14'],
        ['Machtsstrategie', '1', '2', '3', '2', '3', '11'],
        ['Onderhandelingsstrategie', '4', '4', '4', '4', '4', '20'],
        ['Verleidingsstrategie', '3', '3', '3', '3', '3', '15']
    ]
    
    create_proper_table(doc, 'Tabel 5.2: Beslissingsmatrix veranderstrategieën (Boonstra)', boonstra_headers, boonstra_data)
    
    # Add Boonstra decision matrix visual
    add_visual_with_proper_caption(doc, 'Visual_9_Boonstra_Beslissingsmatrix.png', 
                                 'Figuur 5.1: Beslissingsmatrix voor veranderstrategieën volgens Boonstra')
    
    # 5.2.2 Boonstra change strategy
    doc.add_heading('5.2.2 Veranderstrategie Boonstra', 2)
    boonstra_strategy = """Op basis van de beslissingsmatrix wordt de ontwikkelingsstrategie van Boonstra gekozen als de meest geschikte aanpak voor Euro Caps. Deze strategie kenmerkt zich door een participatieve benadering waarbij medewerkers actief betrokken worden bij het veranderproces. De ontwikkelingsstrategie sluit aan bij de bestaande cultuur van Euro Caps die openstaat voor innovatie en verbetering, en past bij de aard van Six Sigma die sterk afhankelijk is van medewerkersbetrokkenheid en continue leren."""
    doc.add_paragraph(boonstra_strategy)
    
    return doc

def add_kotter_implementation(doc):
    """Add detailed Kotter implementation plan"""
    
    # 5.2.3 Kotter approach
    doc.add_heading('5.2.3 Veranderaanpak Kotter', 2)
    
    kotter_intro = """De implementatie van de Six Sigma aanpak bij Euro Caps zal gefaseerd plaatsvinden over een periode van 21 maanden, verdeeld in drie hoofdfasen, geïntegreerd met Kotter's acht stappen:"""
    doc.add_paragraph(kotter_intro)
    
    # Phase 1
    phase1_title = doc.add_paragraph()
    phase1_run = phase1_title.add_run('Fase 1: Voorbereiding (3 maanden)')
    phase1_run.font.bold = True
    phase1_run.font.size = Pt(12)
    
    phase1_text = """Deze fase correspondeert met Kotter's stappen 1, 2, en 3. De focus ligt op het creëren van een stevige basis en een duidelijke routekaart voor de verandering.

1. Creëer een gevoel van urgentie: De noodzaak van continue kwaliteitsverbetering en efficiëntie om concurrentievoordeel te behouden, wordt door CEO Nils Clement en Manager Bedrijfsvoering Servé Bosland intern benadrukt.

2. Vorm een leidende coalitie: Een multidisciplinaire werkgroep wordt samengesteld, bestaande uit belangrijke stakeholders met voldoende autoriteit en expertise om de verandering te sturen.

3. Ontwikkel een visie en strategie: De leidende coalitie definieert een heldere visie die Euro Caps positioneert als een leidende, innoverende en lerende organisatie."""
    doc.add_paragraph(phase1_text)
    
    # Phase 2
    phase2_title = doc.add_paragraph()
    phase2_run = phase2_title.add_run('Fase 2: Implementatie (12 maanden)')
    phase2_run.font.bold = True
    phase2_run.font.size = Pt(12)
    
    phase2_text = """Deze fase omvat Kotter's stappen 4, 5, en 6, gericht op het daadwerkelijk doorvoeren van de veranderingen.

4. Communiceer de veranderingsvisie: De visie wordt breed en herhaaldelijk gecommuniceerd via diverse interne communicatiekanalen om draagvlak te creëren en te onderhouden.

5. Creëer draagvlak voor actie: De leidende coalitie identificeert en verwijdert structurele obstakels die de implementatie van Six Sigma en de bredere verandering belemmeren.

6. Genereer korte termijn successen: Snelle, zichtbare successen uit kleinschalige Six Sigma projecten worden gevierd en breed gecommuniceerd om motivatie te verhogen."""
    doc.add_paragraph(phase2_text)
    
    # Phase 3
    phase3_title = doc.add_paragraph()
    phase3_run = phase3_title.add_run('Fase 3: Verankering (6 maanden)')
    phase3_run.font.bold = True
    phase3_run.font.size = Pt(12)
    
    phase3_text = """Deze finale fase omvat Kotter's stappen 7 en 8, gericht op duurzame borging.

7. Consolideer verbeteringen en produceer nog meer verandering: Na de eerste successen worden de geleerde lessen uit de DMAIC-cycli systematisch gebruikt om verdere verbeteringen te initiëren.

8. Veranker nieuwe benaderingen in de cultuur: De nieuwe structuren, processen en gedragingen worden diepgaand geborgd in het beleid, de prestatiebeoordeling en de interne communicatie van Euro Caps."""
    doc.add_paragraph(phase3_text)
    
    # Add DMAIC-Kotter integration visual
    add_visual_with_proper_caption(doc, 'Visual_10_DMAIC_Kotter_Integratie.png', 
                                 'Figuur 5.2: Integratie van DMAIC-cyclus met Kotter\'s 8-stappenmodel')
    
    return doc

if __name__ == "__main__":
    print("=== Adding Remaining Chapters ===")
    
    # Load existing document
    doc = Document('Adviesrapport_Veranderingsmanagement_FINAL_COMPLETE_CORRECTED.docx')
    
    # Add Chapter 5
    doc = add_chapter_5_complete(doc)
    doc = add_kotter_implementation(doc)
    
    # Save updated document
    doc.save('Adviesrapport_Veranderingsmanagement_VOLLEDIG_COMPLEET.docx')
    print("All chapters added successfully!")
