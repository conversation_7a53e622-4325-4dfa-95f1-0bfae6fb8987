<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix B.V. - Huidige Architectuur" id="id-a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder-123456789" type="strategy"/>
  <folder name="Business" id="id-business-folder-987654321" type="business">
    <element xsi:type="archimate:BusinessActor" name="Grote Klanten" id="id-grote-klanten-001"/>
    <element xsi:type="archimate:BusinessActor" name="Kleine Klanten" id="id-kleine-klanten-002"/>
    <element xsi:type="archimate:BusinessActor" name="Medewerker Orderverwerking" id="id-medewerker-order-003"/>
    <element xsi:type="archimate:BusinessActor" name="Magazijnmedewerker" id="id-magazijn-medewerker-004"/>
    <element xsi:type="archimate:BusinessActor" name="Klantenservice Medewerker" id="id-klantenservice-005"/>
    <element xsi:type="archimate:BusinessActor" name="Vervoerder" id="id-vervoerder-006"/>
    
    <element xsi:type="archimate:BusinessProcess" name="Orderverwerking" id="id-orderverwerking-101"/>
    <element xsi:type="archimate:BusinessProcess" name="Order Ontvangen" id="id-order-ontvangen-102"/>
    <element xsi:type="archimate:BusinessProcess" name="Handmatige Invoer ERP" id="id-handmatige-invoer-erp-103"/>
    <element xsi:type="archimate:BusinessProcess" name="Handmatige Invoer WMS" id="id-handmatige-invoer-wms-104"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantgegevens Controleren" id="id-klant-controle-105"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad Valideren" id="id-voorraad-validatie-106"/>
    
    <element xsi:type="archimate:BusinessProcess" name="Magazijnbeheer" id="id-magazijnbeheer-201"/>
    <element xsi:type="archimate:BusinessProcess" name="Picking" id="id-picking-202"/>
    <element xsi:type="archimate:BusinessProcess" name="Packing" id="id-packing-203"/>
    <element xsi:type="archimate:BusinessProcess" name="Handmatige Label Invoer" id="id-handmatige-label-204"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzending" id="id-verzending-205"/>
    <element xsi:type="archimate:BusinessProcess" name="Tracking Kopiëren" id="id-tracking-kopieren-206"/>
    
    <element xsi:type="archimate:BusinessProcess" name="Klantenservice" id="id-klantenservice-proces-301"/>
    <element xsi:type="archimate:BusinessProcess" name="Status Opzoeken" id="id-status-opzoeken-302"/>
    <element xsi:type="archimate:BusinessProcess" name="Informatie Samenvoegen" id="id-info-samenvoegen-303"/>
    
    <element xsi:type="archimate:BusinessObject" name="Order" id="id-order-401"/>
    <element xsi:type="archimate:BusinessObject" name="Klantgegevens" id="id-klantgegevens-402"/>
    <element xsi:type="archimate:BusinessObject" name="Voorraadgegevens" id="id-voorraadgegevens-403"/>
    <element xsi:type="archimate:BusinessObject" name="Picklijst" id="id-picklijst-404"/>
    <element xsi:type="archimate:BusinessObject" name="Verzendlabel" id="id-verzendlabel-405"/>
    <element xsi:type="archimate:BusinessObject" name="Trackingnummer" id="id-trackingnummer-406"/>
    
    <element xsi:type="archimate:BusinessService" name="E-fulfilment Service" id="id-efulfilment-service-501"/>
    <element xsi:type="archimate:BusinessService" name="Opslag en Distributie" id="id-opslag-distributie-502"/>
  </folder>
  <folder name="Application" id="id-application-folder-111222333" type="application">
    <element xsi:type="archimate:ApplicationComponent" name="ERP Systeem" id="id-erp-systeem-601"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS (Warehouse Management)" id="id-wms-systeem-602"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS (Transport Management)" id="id-tms-systeem-603"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM Systeem" id="id-crm-systeem-604"/>
    <element xsi:type="archimate:ApplicationComponent" name="Verouderde API" id="id-verouderde-api-605"/>
    
    <element xsi:type="archimate:ApplicationService" name="Klantbeheer" id="id-klantbeheer-service-701"/>
    <element xsi:type="archimate:ApplicationService" name="Financiën en Facturatie" id="id-financien-service-702"/>
    <element xsi:type="archimate:ApplicationService" name="Magazijnprocessen" id="id-magazijn-service-703"/>
    <element xsi:type="archimate:ApplicationService" name="Voorraadvalidatie" id="id-voorraad-service-704"/>
    <element xsi:type="archimate:ApplicationService" name="Verzendlabels" id="id-verzendlabel-service-705"/>
    <element xsi:type="archimate:ApplicationService" name="Vervoerder Communicatie" id="id-vervoerder-service-706"/>
    <element xsi:type="archimate:ApplicationService" name="Klantcontact Registratie" id="id-klantcontact-service-707"/>
    
    <element xsi:type="archimate:DataObject" name="Orderdata" id="id-orderdata-801"/>
    <element xsi:type="archimate:DataObject" name="Klantdata" id="id-klantdata-802"/>
    <element xsi:type="archimate:DataObject" name="Voorraaddata" id="id-voorraaddata-803"/>
    <element xsi:type="archimate:DataObject" name="Verzenddata" id="id-verzenddata-804"/>
    <element xsi:type="archimate:DataObject" name="Trackingdata" id="id-trackingdata-805"/>
    <element xsi:type="archimate:DataObject" name="CRM Data" id="id-crm-data-806"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder-444555666" type="technology">
    <element xsi:type="archimate:Node" name="On-premise Server (WMS)" id="id-onpremise-server-901"/>
    <element xsi:type="archimate:Node" name="Cloud Platform (ERP)" id="id-cloud-platform-902"/>
    <element xsi:type="archimate:Node" name="Standalone Server (TMS)" id="id-standalone-server-903"/>
    <element xsi:type="archimate:Node" name="CRM Server" id="id-crm-server-904"/>
    
    <element xsi:type="archimate:CommunicationNetwork" name="Bedrijfsnetwerk" id="id-bedrijfsnetwerk-1001"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Internet" id="id-internet-1002"/>
    
    <element xsi:type="archimate:Device" name="Werkstation Orderverwerking" id="id-werkstation-order-1101"/>
    <element xsi:type="archimate:Device" name="Werkstation Magazijn" id="id-werkstation-magazijn-1102"/>
    <element xsi:type="archimate:Device" name="Werkstation Klantenservice" id="id-werkstation-service-1103"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder-777888999" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder-000111222" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder-333444555" type="other"/>
  <folder name="Relations" id="id-relations-folder-666777888" type="relations">
    <element xsi:type="archimate:TriggeringRelationship" id="rel-grote-klanten-order-2001" source="id-grote-klanten-001" target="id-order-ontvangen-102"/>
    <element xsi:type="archimate:TriggeringRelationship" id="rel-kleine-klanten-order-2002" source="id-kleine-klanten-002" target="id-order-ontvangen-102"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-medewerker-orderverwerking-2003" source="id-medewerker-order-003" target="id-orderverwerking-101"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-magazijn-medewerker-magazijn-2004" source="id-magazijn-medewerker-004" target="id-magazijnbeheer-201"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-klantenservice-proces-2005" source="id-klantenservice-005" target="id-klantenservice-proces-301"/>
    
    <element xsi:type="archimate:CompositionRelationship" id="rel-orderverwerking-ontvangen-2101" source="id-orderverwerking-101" target="id-order-ontvangen-102"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-orderverwerking-erp-2102" source="id-orderverwerking-101" target="id-handmatige-invoer-erp-103"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-orderverwerking-wms-2103" source="id-orderverwerking-101" target="id-handmatige-invoer-wms-104"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-orderverwerking-klant-2104" source="id-orderverwerking-101" target="id-klant-controle-105"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-orderverwerking-voorraad-2105" source="id-orderverwerking-101" target="id-voorraad-validatie-106"/>
    
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-picking-2201" source="id-magazijnbeheer-201" target="id-picking-202"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-packing-2202" source="id-magazijnbeheer-201" target="id-packing-203"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-label-2203" source="id-magazijnbeheer-201" target="id-handmatige-label-204"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-verzending-2204" source="id-magazijnbeheer-201" target="id-verzending-205"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-tracking-2205" source="id-magazijnbeheer-201" target="id-tracking-kopieren-206"/>
    
    <element xsi:type="archimate:CompositionRelationship" id="rel-klantenservice-status-2301" source="id-klantenservice-proces-301" target="id-status-opzoeken-302"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-klantenservice-samenvoegen-2302" source="id-klantenservice-proces-301" target="id-info-samenvoegen-303"/>
    
    <element xsi:type="archimate:FlowRelationship" id="rel-ontvangen-erp-2401" source="id-order-ontvangen-102" target="id-handmatige-invoer-erp-103"/>
    <element xsi:type="archimate:FlowRelationship" id="rel-ontvangen-wms-2402" source="id-order-ontvangen-102" target="id-handmatige-invoer-wms-104"/>
    <element xsi:type="archimate:FlowRelationship" id="rel-erp-klant-2403" source="id-handmatige-invoer-erp-103" target="id-klant-controle-105"/>
    <element xsi:type="archimate:FlowRelationship" id="rel-wms-voorraad-2404" source="id-handmatige-invoer-wms-104" target="id-voorraad-validatie-106"/>
    <element xsi:type="archimate:FlowRelationship" id="rel-picking-packing-2405" source="id-picking-202" target="id-packing-203"/>
    <element xsi:type="archimate:FlowRelationship" id="rel-packing-label-2406" source="id-packing-203" target="id-handmatige-label-204"/>
    <element xsi:type="archimate:FlowRelationship" id="rel-label-verzending-2407" source="id-handmatige-label-204" target="id-verzending-205"/>
    <element xsi:type="archimate:FlowRelationship" id="rel-verzending-tracking-2408" source="id-verzending-205" target="id-tracking-kopieren-206"/>
    
    <element xsi:type="archimate:RealizationRelationship" id="rel-erp-klantbeheer-2501" source="id-erp-systeem-601" target="id-klantbeheer-service-701"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-erp-financien-2502" source="id-erp-systeem-601" target="id-financien-service-702"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-wms-magazijn-2503" source="id-wms-systeem-602" target="id-magazijn-service-703"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-wms-voorraad-2504" source="id-wms-systeem-602" target="id-voorraad-service-704"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-tms-verzendlabel-2505" source="id-tms-systeem-603" target="id-verzendlabel-service-705"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-tms-vervoerder-2506" source="id-tms-systeem-603" target="id-vervoerder-service-706"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-crm-klantcontact-2507" source="id-crm-systeem-604" target="id-klantcontact-service-707"/>
    
    <element xsi:type="archimate:ServingRelationship" id="rel-klantbeheer-klantcontrole-2601" source="id-klantbeheer-service-701" target="id-klant-controle-105"/>
    <element xsi:type="archimate:ServingRelationship" id="rel-voorraad-validatie-2602" source="id-voorraad-service-704" target="id-voorraad-validatie-106"/>
    <element xsi:type="archimate:ServingRelationship" id="rel-magazijn-picking-2603" source="id-magazijn-service-703" target="id-picking-202"/>
    <element xsi:type="archimate:ServingRelationship" id="rel-verzendlabel-label-2604" source="id-verzendlabel-service-705" target="id-handmatige-label-204"/>
    <element xsi:type="archimate:ServingRelationship" id="rel-klantcontact-status-2605" source="id-klantcontact-service-707" target="id-status-opzoeken-302"/>
    
    <element xsi:type="archimate:AccessRelationship" id="rel-erp-orderdata-2701" source="id-erp-systeem-601" target="id-orderdata-801"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-erp-klantdata-2702" source="id-erp-systeem-601" target="id-klantdata-802"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-wms-voorraaddata-2703" source="id-wms-systeem-602" target="id-voorraaddata-803"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-tms-verzenddata-2704" source="id-tms-systeem-603" target="id-verzenddata-804"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-tms-trackingdata-2705" source="id-tms-systeem-603" target="id-trackingdata-805"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-crm-crmdata-2706" source="id-crm-systeem-604" target="id-crm-data-806"/>
    
    <element xsi:type="archimate:RealizationRelationship" id="rel-onpremise-wms-2801" source="id-onpremise-server-901" target="id-wms-systeem-602"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-cloud-erp-2802" source="id-cloud-platform-902" target="id-erp-systeem-601"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-standalone-tms-2803" source="id-standalone-server-903" target="id-tms-systeem-603"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-crm-server-crm-2804" source="id-crm-server-904" target="id-crm-systeem-604"/>
    
    <element xsi:type="archimate:AssociationRelationship" id="rel-bedrijfsnetwerk-onpremise-2901" source="id-bedrijfsnetwerk-1001" target="id-onpremise-server-901"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-bedrijfsnetwerk-standalone-2902" source="id-bedrijfsnetwerk-1001" target="id-standalone-server-903"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-bedrijfsnetwerk-crm-2903" source="id-bedrijfsnetwerk-1001" target="id-crm-server-904"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-internet-cloud-2904" source="id-internet-1002" target="id-cloud-platform-902"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-bedrijfsnetwerk-internet-2905" source="id-bedrijfsnetwerk-1001" target="id-internet-1002"/>
    
    <element xsi:type="archimate:AssociationRelationship" id="rel-werkstation-order-netwerk-3001" source="id-werkstation-order-1101" target="id-bedrijfsnetwerk-1001"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-werkstation-magazijn-netwerk-3002" source="id-werkstation-magazijn-1102" target="id-bedrijfsnetwerk-1001"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-werkstation-service-netwerk-3003" source="id-werkstation-service-1103" target="id-bedrijfsnetwerk-1001"/>
  </folder>
  <folder name="Views" id="id-views-folder-999888777" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix B.V. - Huidige Architectuur Overzicht" id="id-main-view-4001">
      <child xsi:type="archimate:DiagramObject" id="diag-grote-klanten-5001" archimateElement="id-grote-klanten-001">
        <bounds x="50" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-grote-klanten-order-6001" source="diag-grote-klanten-5001" target="diag-order-ontvangen-5003" archimateRelationship="rel-grote-klanten-order-2001"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-kleine-klanten-5002" archimateElement="id-kleine-klanten-002">
        <bounds x="200" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-kleine-klanten-order-6002" source="diag-kleine-klanten-5002" target="diag-order-ontvangen-5003" archimateRelationship="rel-kleine-klanten-order-2002"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-orderverwerking-5010" archimateElement="id-orderverwerking-101">
        <bounds x="50" y="150" width="600" height="200"/>
        <child xsi:type="archimate:DiagramObject" id="diag-order-ontvangen-5003" archimateElement="id-order-ontvangen-102">
          <bounds x="20" y="30" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-ontvangen-erp-6003" source="diag-order-ontvangen-5003" target="diag-handmatige-invoer-erp-5004" archimateRelationship="rel-ontvangen-erp-2401"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-ontvangen-wms-6004" source="diag-order-ontvangen-5003" target="diag-handmatige-invoer-wms-5005" archimateRelationship="rel-ontvangen-wms-2402"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-handmatige-invoer-erp-5004" archimateElement="id-handmatige-invoer-erp-103">
          <bounds x="170" y="30" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-erp-klant-6005" source="diag-handmatige-invoer-erp-5004" target="diag-klant-controle-5006" archimateRelationship="rel-erp-klant-2403"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-handmatige-invoer-wms-5005" archimateElement="id-handmatige-invoer-wms-104">
          <bounds x="170" y="110" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-wms-voorraad-6006" source="diag-handmatige-invoer-wms-5005" target="diag-voorraad-validatie-5007" archimateRelationship="rel-wms-voorraad-2404"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-klant-controle-5006" archimateElement="id-klant-controle-105">
          <bounds x="320" y="30" width="120" height="55"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-voorraad-validatie-5007" archimateElement="id-voorraad-validatie-106">
          <bounds x="320" y="110" width="120" height="55"/>
        </child>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-magazijnbeheer-5020" archimateElement="id-magazijnbeheer-201">
        <bounds x="50" y="400" width="800" height="150"/>
        <child xsi:type="archimate:DiagramObject" id="diag-picking-5008" archimateElement="id-picking-202">
          <bounds x="20" y="30" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-picking-packing-6007" source="diag-picking-5008" target="diag-packing-5009" archimateRelationship="rel-picking-packing-2405"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-packing-5009" archimateElement="id-packing-203">
          <bounds x="170" y="30" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-packing-label-6008" source="diag-packing-5009" target="diag-handmatige-label-5010" archimateRelationship="rel-packing-label-2406"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-handmatige-label-5010" archimateElement="id-handmatige-label-204">
          <bounds x="320" y="30" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-label-verzending-6009" source="diag-handmatige-label-5010" target="diag-verzending-5011" archimateRelationship="rel-label-verzending-2407"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-verzending-5011" archimateElement="id-verzending-205">
          <bounds x="470" y="30" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="conn-verzending-tracking-6010" source="diag-verzending-5011" target="diag-tracking-kopieren-5012" archimateRelationship="rel-verzending-tracking-2408"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-tracking-kopieren-5012" archimateElement="id-tracking-kopieren-206">
          <bounds x="620" y="30" width="120" height="55"/>
        </child>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-klantenservice-proces-5030" archimateElement="id-klantenservice-proces-301">
        <bounds x="900" y="150" width="300" height="150"/>
        <child xsi:type="archimate:DiagramObject" id="diag-status-opzoeken-5013" archimateElement="id-status-opzoeken-302">
          <bounds x="20" y="30" width="120" height="55"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-info-samenvoegen-5014" archimateElement="id-info-samenvoegen-303">
          <bounds x="160" y="30" width="120" height="55"/>
        </child>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-erp-systeem-5040" archimateElement="id-erp-systeem-601">
        <bounds x="100" y="600" width="150" height="80"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-erp-klantbeheer-6011" source="diag-erp-systeem-5040" target="diag-klant-controle-5006" archimateRelationship="rel-klantbeheer-klantcontrole-2601"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-wms-systeem-5041" archimateElement="id-wms-systeem-602">
        <bounds x="300" y="600" width="150" height="80"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-wms-voorraad-6012" source="diag-wms-systeem-5041" target="diag-voorraad-validatie-5007" archimateRelationship="rel-voorraad-validatie-2602"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-wms-picking-6013" source="diag-wms-systeem-5041" target="diag-picking-5008" archimateRelationship="rel-magazijn-picking-2603"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-tms-systeem-5042" archimateElement="id-tms-systeem-603">
        <bounds x="500" y="600" width="150" height="80"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-tms-label-6014" source="diag-tms-systeem-5042" target="diag-handmatige-label-5010" archimateRelationship="rel-verzendlabel-label-2604"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-crm-systeem-5043" archimateElement="id-crm-systeem-604">
        <bounds x="950" y="600" width="150" height="80"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-crm-status-6015" source="diag-crm-systeem-5043" target="diag-status-opzoeken-5013" archimateRelationship="rel-klantcontact-status-2605"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-cloud-platform-5050" archimateElement="id-cloud-platform-902">
        <bounds x="100" y="750" width="150" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-cloud-erp-6016" source="diag-cloud-platform-5050" target="diag-erp-systeem-5040" archimateRelationship="rel-cloud-erp-2802"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-onpremise-server-5051" archimateElement="id-onpremise-server-901">
        <bounds x="300" y="750" width="150" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-onpremise-wms-6017" source="diag-onpremise-server-5051" target="diag-wms-systeem-5041" archimateRelationship="rel-onpremise-wms-2801"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-standalone-server-5052" archimateElement="id-standalone-server-903">
        <bounds x="500" y="750" width="150" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-standalone-tms-6018" source="diag-standalone-server-5052" target="diag-tms-systeem-5042" archimateRelationship="rel-standalone-tms-2803"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-crm-server-5053" archimateElement="id-crm-server-904">
        <bounds x="950" y="750" width="150" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-crm-server-crm-6019" source="diag-crm-server-5053" target="diag-crm-systeem-5043" archimateRelationship="rel-crm-server-crm-2804"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-bedrijfsnetwerk-5060" archimateElement="id-bedrijfsnetwerk-1001">
        <bounds x="200" y="850" width="600" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-netwerk-onpremise-6020" source="diag-bedrijfsnetwerk-5060" target="diag-onpremise-server-5051" archimateRelationship="rel-bedrijfsnetwerk-onpremise-2901"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-netwerk-standalone-6021" source="diag-bedrijfsnetwerk-5060" target="diag-standalone-server-5052" archimateRelationship="rel-bedrijfsnetwerk-standalone-2902"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-netwerk-crm-6022" source="diag-bedrijfsnetwerk-5060" target="diag-crm-server-5053" archimateRelationship="rel-bedrijfsnetwerk-crm-2903"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-netwerk-internet-6023" source="diag-bedrijfsnetwerk-5060" target="diag-internet-5061" archimateRelationship="rel-bedrijfsnetwerk-internet-2905"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-internet-5061" archimateElement="id-internet-1002">
        <bounds x="50" y="850" width="120" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-internet-cloud-6024" source="diag-internet-5061" target="diag-cloud-platform-5050" archimateRelationship="rel-internet-cloud-2904"/>
      </child>
    </element>
  </folder>
</archimate:model>
