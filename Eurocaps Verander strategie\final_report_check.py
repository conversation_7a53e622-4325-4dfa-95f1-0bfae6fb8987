import os
import docx
from docx.shared import Inches

def final_report_check():
    # Pad naar het rapport
    report_path = "Adviesrapport Veranderingsmanagement-<PERSON><PERSON>-1066741.docx"
    
    if not os.path.exists(report_path):
        print(f"Rapport niet gevonden: {report_path}")
        return
    
    # Open het document
    doc = docx.Document(report_path)
    
    print("=== FINALE CONTROLE ADVIESRAPPORT ===\n")
    
    # Tel secties en content
    sections = []
    images = []
    paragraphs = 0
    
    for para in doc.paragraphs:
        paragraphs += 1
        if para.style and para.style.name and para.style.name.startswith('Heading'):
            sections.append(para.text)
    
    for rel in doc.part.rels.values():
        if "image" in rel.target_ref:
            images.append(rel.target_ref)
    
    print(f"📊 RAPPORT STATISTIEKEN:")
    print(f"   • Aantal secties: {len(sections)}")
    print(f"   • Aantal paragrafen: {paragraphs}")
    print(f"   • Aantal afbeeldingen: {len(images)}")
    print(f"   • Bestandsgrootte: {os.path.getsize(report_path) / 1024 / 1024:.1f} MB")
    
    print(f"\n📋 SECTIES GEVONDEN:")
    for i, section in enumerate(sections, 1):
        print(f"   {i}. {section}")
    
    # Controleer essentiële onderdelen
    print(f"\n✅ ESSENTIËLE ONDERDELEN:")
    
    essential_items = [
        "Voorwoord", "Managementsamenvatting", "Inleiding", "Theoretisch Kader",
        "Huidige Situatie", "Stakeholderanalyse", "Gewenste Situatie", 
        "Veranderstrategie", "Implementatieplan", "Communicatieplan",
        "Risicoanalyse", "Conclusie", "Aanbevelingen", "Literatuurlijst", "Bijlagen"
    ]
    
    found_items = []
    missing_items = []
    
    for item in essential_items:
        if any(item.lower() in section.lower() for section in sections):
            found_items.append(item)
            print(f"   ✅ {item}")
        else:
            missing_items.append(item)
            print(f"   ❌ {item}")
    
    print(f"\n📈 KWALITEITSSAMENVATTING:")
    coverage = len(found_items) / len(essential_items) * 100
    print(f"   • Dekking essentiële onderdelen: {coverage:.1f}%")
    
    if coverage >= 90:
        print(f"   • Status: UITSTEKEND - Rapport is compleet")
    elif coverage >= 80:
        print(f"   • Status: GOED - Rapport is bijna compleet")
    elif coverage >= 70:
        print(f"   • Status: REDELIJK - Rapport heeft nog wat aanvullingen nodig")
    else:
        print(f"   • Status: ONVOLDOENDE - Rapport heeft significante aanvullingen nodig")
    
    print(f"\n🎯 AANBEVELINGEN VOOR VERBETERING:")
    
    if missing_items:
        print(f"   • Voeg ontbrekende secties toe: {', '.join(missing_items)}")
    
    if len(images) < 10:
        print(f"   • Voeg meer visuals toe voor betere ondersteuning")
    
    if paragraphs < 100:
        print(f"   • Uitbreiding van content voor meer diepgang")
    
    print(f"\n📝 VOLGENDE STAPPEN:")
    print(f"   1. Controleer spelling en grammatica")
    print(f"   2. Voeg paginanummers toe")
    print(f"   3. Genereer automatische inhoudsopgave")
    print(f"   4. Controleer consistentie in opmaak")
    print(f"   5. Voeg eventueel ontbrekende bronnen toe")
    
    print(f"\n🎉 CONCLUSIE:")
    if coverage >= 85:
        print(f"   Je adviesrapport is uitstekend gestructureerd en bevat alle essentiële onderdelen!")
        print(f"   Het rapport is klaar voor inlevering of verdere verfijning.")
    else:
        print(f"   Het rapport heeft nog enkele aanvullingen nodig, maar de basis is goed.")
        print(f"   Focus op de ontbrekende onderdelen voor een compleet rapport.")
    
    print(f"\n📁 BESCHIKBARE BRONNEN:")
    print(f"   • Alle PowerPoint visuals zijn geëxporteerd als PNG")
    print(f"   • Stakeholderanalyse document is beschikbaar")
    print(f"   • Scripts voor verdere aanpassingen zijn aanwezig")
    print(f"   • Ondersteunende documenten en data zijn beschikbaar")

if __name__ == "__main__":
    final_report_check() 