# Python string method examples in strict 3-line pattern
# 1) text = [...]
# 2) <method_name> = [word.<method>(...) for word in text]  (or consistent 3-line variant if not per-word)
# 3) print(<method_name>)

# capitalize
text = ["hallo", "wereld", "goed"]
capitalize = [word.capitalize() for word in text]
print(capitalize)

# casefold
text = ["Straße", "HELLO"]
casefold = [word.casefold() for word in text]
print(casefold)

# center
text = ["hi", "ok"]
center = [word.center(6, "-") for word in text]
print(center)

# count
text = ["banana", "ananas"]
count = [word.count("na") for word in text]
print(count)

# encode (to bytes)
text = ["café", "naïve"]
encode = [word.encode("utf-8") for word in text]
print(encode)

# endswith
text = ["file.py", "main.txt"]
endswith = [word.endswith(".py") for word in text]
print(endswith)

# expandtabs
text = ["a\tb", "1\t2"]
expandtabs = [word.expandtabs(4) for word in text]
print(expandtabs)

# find
text = ["banana", "ananas"]
find = [word.find("na") for word in text]
print(find)

# format (applied on a format string using each word)
text = ["Piet", "Klaas"]
format = ["Hallo {}".format(word) for word in text]
print(format)

# format_map (per mapping in text)
text = [{"x": 1, "y": 2}, {"x": 10, "y": 20}]
format_map = ["{x}-{y}".format_map(word) for word in text]
print(format_map)

# index
text = ["banana", "ananas"]
index = [word.index("na") for word in text]
print(index)

# isalnum
text = ["abc123", "abc!", "123"]
isalnum = [word.isalnum() for word in text]
print(isalnum)

# isalpha
text = ["abc", "abc123"]
isalpha = [word.isalpha() for word in text]
print(isalpha)

# isascii
text = ["abc", "é"]
isascii = [word.isascii() for word in text]
print(isascii)

# isdecimal
text = ["10", "½"]
isdecimal = [word.isdecimal() for word in text]
print(isdecimal)

# isdigit
text = ["10", "²"]
isdigit = [word.isdigit() for word in text]
print(isdigit)

# isidentifier
text = ["var_1", "1abc"]
isidentifier = [word.isidentifier() for word in text]
print(isidentifier)

# islower
text = ["abc", "Abc"]
islower = [word.islower() for word in text]
print(islower)

# isnumeric
text = ["10", "½", "²"]
isnumeric = [word.isnumeric() for word in text]
print(isnumeric)

# isprintable
text = ["abc", "\n"]
isprintable = [word.isprintable() for word in text]
print(isprintable)

# isspace
text = ["   ", "\t", "a"]
isspace = [word.isspace() for word in text]
print(isspace)

# istitle
text = ["Hello World", "Hello world"]
istitle = [word.istitle() for word in text]
print(istitle)

# isupper
text = ["ABC", "AbC"]
isupper = [word.isupper() for word in text]
print(isupper)

# join (3-line variant without comprehension)
text = ["a", "b", "c"]
join = ["-".join(text)]
print(join)

# ljust
text = ["hi", "ok"]
ljust = [word.ljust(5, ".") for word in text]
print(ljust)

# lower
text = ["AbC", "XYZ"]
lower = [word.lower() for word in text]
print(lower)

# lstrip
text = ["  hi", "\t ok"]
lstrip = [word.lstrip() for word in text]
print(lstrip)

# maketrans (construct table; printed from first element)
text = ["eat", "bee"]
maketrans = [str.maketrans({"e": "3", "a": "@"}) for _ in text]
print(maketrans[0])

# partition
text = ["a-b-c", "1-2-3"]
partition = [word.partition("-") for word in text]
print(partition)

# replace
text = ["hello", "ball"]
replace = [word.replace("l", "x", 1) for word in text]
print(replace)

# rfind
text = ["banana", "ananas"]
rfind = [word.rfind("na") for word in text]
print(rfind)

# rindex
text = ["banana", "ananas"]
rindex = [word.rindex("na") for word in text]
print(rindex)

# rjust
text = ["hi", "ok"]
rjust = [word.rjust(5, ".") for word in text]
print(rjust)

# rpartition
text = ["a-b-c", "1-2-3"]
rpartition = [word.rpartition("-") for word in text]
print(rpartition)

# rsplit (limit 1 from the right)
text = ["a b c", "1 2 3"]
rsplit = [word.rsplit(None, 1) for word in text]
print(rsplit)

# rstrip
text = ["hi  ", "ok..."]
rstrip = [word.rstrip() for word in text]
print(rstrip)

# split
text = ["a,b,c", "1,2,3"]
split = [word.split(",") for word in text]
print(split)

# splitlines
text = ["a\nb", "x\ny"]
splitlines = [word.splitlines() for word in text]
print(splitlines)

# startswith
text = ["hello", "world"]
startswith = [word.startswith("he") for word in text]
print(startswith)

# strip
text = ["  hi  ", "..ok.."]
strip = [word.strip(" .") for word in text]
print(strip)

# swapcase
text = ["AbC", "xYz"]
swapcase = [word.swapcase() for word in text]
print(swapcase)

# title
text = ["hello world", "good-day"]
title = [word.title() for word in text]
print(title)

# translate (using inline maketrans)
text = ["eat", "bee"]
translate = [word.translate(str.maketrans({"e": "3", "a": "@"})) for word in text]
print(translate)

# upper
text = ["abc", "xYz"]
upper = [word.upper() for word in text]
print(upper)

