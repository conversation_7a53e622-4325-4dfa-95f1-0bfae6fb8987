{\rtf1\ansi\deff0
{\fonttbl{\f0 Arial;}}
\fs16
\b Python Cheat Sheet v3 – Pagina 1 (ALLE modules: 1 regel + mini-uitleg)\b0\line
Let op: klein font; 1 regel per methode. Print dubbelzijdig met deze en pagina 2.\line
\line
\b String-methoden\b0\line
capitalize  \tab print([w.capitalize() for w in ["hi","there"]])           \tab Eerste letter hoofd\line
casefold    \tab print([w.casefold() for w in ["Straße","HELLO"]])         \tab Sterke lower\line
center      \tab print([w.center(6,'-') for w in ["hi","ok"]])              \tab Uitvullen midden\line
count       \tab print([w.count('na') for w in ["banana","ananas"]])         \tab Aantal sub\line
encode      \tab print([w.encode('utf-8') for w in ["café","naïve"]])         \tab Naar bytes\line
endswith    \tab print([w.endswith('.py') for w in ["a.py","b.txt"]])         \tab Eindigt op\line
expandtabs  \tab print([w.expandtabs(4) for w in ["a\tb","1\t2"]])           \tab Tabs→spaties\line
find        \tab print([w.find('na') for w in ["banana","ananas"]])           \tab Eerste index/-1\line
format      \tab print(["Hoi {}".format(x) for x in ["Piet","Klaas"]])        \tab Vul {}\line
format_map  \tab print(["{x}-{y}".format_map(m) for m in [{\'27}x\'3a1,{\'27}y\'3a2}]]) \tab Vul met dict\line
index       \tab print([w.index('na') for w in ["banana","ananas"]])          \tab Eerste index / error\line
isalnum     \tab print([w.isalnum() for w in ["abc123","abc!"]])              \tab Letters/cijfers\line
isalpha     \tab print([w.isalpha() for w in ["abc","abc1"]])                 \tab Alleen letters\line
isascii     \tab print([w.isascii() for w in ["abc","é"]])                    \tab ASCII?\line
isdecimal   \tab print([w.isdecimal() for w in ["10","½"]])                   \tab Decimale digits\line
isdigit     \tab print([w.isdigit() for w in ["10","²"]])                     \tab Digits (incl. super)\line
isidentifier\tab print([w.isidentifier() for w in ["var_1","1abc"]])          \tab Geldige naam\line
islower     \tab print([w.islower() for w in ["abc","Abc"]])                  \tab Alles lower?\line
isnumeric   \tab print([w.isnumeric() for w in ["10","½"]])                   \tab Numeriek (breuken)\line
isprintable \tab print([w.isprintable() for w in ["abc","\n"]])               \tab Printable?\line
isspace     \tab print([w.isspace() for w in ["   ","a"]])                     \tab Alleen whitespace\line
istitle     \tab print([w.istitle() for w in ["Hello World","Hello world"]])   \tab Titelcase?\line
isupper     \tab print([w.isupper() for w in ["ABC","AbC"]])                  \tab Alles upper?\line
join        \tab print('-'.join(["a","b","c"]))                             \tab Voeg lijst samen\line
ljust       \tab print([w.ljust(5,'.') for w in ["hi","ok"]])                 \tab Links uitvullen\line
lower       \tab print([w.lower() for w in ["AbC","XYZ"]])                    \tab Naar lowercase\line
lstrip      \tab print([w.lstrip() for w in ["  hi","\tok"]])                \tab Links trim\line
maketrans   \tab print("eat".translate(str.maketrans('ea','3@')))               \tab Translatietabel\line
partition   \tab print([w.partition('-') for w in ["a-b-c","1-2-3"]])          \tab Split in 3-delig\line
replace     \tab print([w.replace('l','x',1) for w in ["hello","ball"]])       \tab Vervangen\line
rfind       \tab print([w.rfind('na') for w in ["banana","ananas"]])           \tab Laatste index/-1\line
rindex      \tab print([w.rindex('na') for w in ["banana","ananas"]])          \tab Laatste index/error\line
rjust       \tab print([w.rjust(5,'.') for w in ["hi","ok"]])                 \tab Rechts uitvullen\line
rpartition  \tab print([w.rpartition('-') for w in ["a-b-c","1-2-3"]])         \tab Rechts 3-delig\line
rsplit      \tab print([w.rsplit(None,1) for w in ["a b c","1 2 3"]])         \tab Rechts splitsen\line
rstrip      \tab print([w.rstrip() for w in ["hi  ","ok..."]])                \tab Rechts trim\line
split       \tab print([w.split(',') for w in ["a,b,c","1,2,3"]])             \tab Splits op sep\line
splitlines  \tab print([w.splitlines() for w in ["a\nb","x\ny"]])             \tab Regels splitsen\line
startswith  \tab print([w.startswith('he') for w in ["hello","world"]])        \tab Begint met\line
strip       \tab print([w.strip(' .') for w in ["  hi  ","..ok.."]])            \tab Trim beide\line
swapcase    \tab print([w.swapcase() for w in ["AbC","xYz"]])                 \tab Wissel case\line
title       \tab print([w.title() for w in ["hello world","good-day"]])       \tab Woorden Titel\line
translate   \tab print([w.translate(str.maketrans({'e':'3','a':'@'})) for w in ["eat","bee"]]) \tab Met tabel\line
upper       \tab print([w.upper() for w in ["abc","xYz"]])                    \tab Naar uppercase\line
zfill       \tab print([w.zfill(4) for w in ["7","42"]])                      \tab Voorloopnullen\line
\line
\b List-methoden\b0\line
append      \tab lst=["a"]; lst.append("b"); print(lst)                        \tab Element achteraan\line
extend      \tab lst=["a"]; lst.extend(["b","c"]); print(lst)                 \tab Lijst toevoegen\line
insert      \tab lst=["a","c"]; lst.insert(1,"b"); print(lst)                \tab Op index invoegen\line
remove      \tab lst=["a","b","a"]; lst.remove("a"); print(lst)              \tab Eerste voorkomen weg\line
pop         \tab lst=["a","b","c"]; print(lst.pop(1), lst)                   \tab Verwijder+return\line
del         \tab lst=["a","b","c"]; del lst[0:2]; print(lst)                 \tab Del element/slice\line
index       \tab lst=["a","b","a"]; print(lst.index("a"))                   \tab Index eerste\line
count       \tab lst=["a","b","a"]; print(lst.count("a"))                   \tab Aantal\line
sort        \tab lst=[3,1,2]; lst.sort(); print(lst)                             \tab Sorteer in-place\line
reverse     \tab lst=[1,2,3]; lst.reverse(); print(lst)                          \tab Keer om in-place\line
\page
\fs16
\b Python Cheat Sheet v3 – Pagina 2 (Uitgebreide voorbeelden, compact)\b0\line
- Patroon: text=["a","b"]; res=[w.METHODE(args) for w in text]; print(res)\line
- join/encode/translate: direct op string of met korte helper.\line
\line
\b String – conversie/uitlijning\b0\line
text=["hi","ok"];  print([w.center(6,'-') for w in text], [w.ljust(5,'.') for w in text])\line
text=["hi","ok"];  print([w.rjust(5,'.') for w in text], [w.swapcase() for w in ["AbC","xYz"]])\line
text=["  hi  ","..ok.."]; print([w.strip(' .') for w in text], [w.lstrip() for w in ["  hi","\tok"]])\line
\line
\b String – zoeken/tellen\b0\line
text=["banana","ananas"]; print([w.count('na') for w in text], [w.find('na') for w in text])\line
text=["banana","ananas"]; print([w.rfind('na') for w in text], [w.index('na') for w in text])\line
text=["banana","ananas"]; print([w.rindex('na') for w in text], [w.startswith('ba') for w in text])\line
\line
\b String – splits/samenvoeg\b0\line
print([w.split(',') for w in ["a,b,c","1,2,3"]], [w.rsplit(None,1) for w in ["a b c","1 2 3"]])\line
print([w.splitlines() for w in ["a\nb","x\ny"]], '-'.join(["a","b","c"]))\line
\line
\b String – format/encode/translate\b0\line
print(["Hoi {}".format(x) for x in ["Piet","Klaas"]])\line
print("{x}-{y}".format_map({\'27}x\'3a1,{\'27}y\'3a2}))\line
print([w.encode('utf-8') for w in ["café","naïve"]])\line
print([w.translate(str.maketrans('ea','3@')) for w in ["eat","bee"]])\line
\line
\b String – is*/upper/lower/title\b0\line
print([w.isupper() for w in ["ABC","AbC"]], [w.islower() for w in ["abc","Abc"]])\line
print([w.istitle() for w in ["Hello World","Hello world"]], [w.title() for w in ["hello world","good-day"]])\line
print([w.isalpha() for w in ["abc","abc1"]], [w.isalnum() for w in ["abc123","abc!"]])\line
print([w.isdecimal() for w in ["10","12"]], [w.isnumeric() for w in ["10","½"]])\line
print([w.isascii() for w in ["abc","é"]], [w.isidentifier() for w in ["var_1","1abc"]])\line
print([w.upper() for w in ["abc","xYz"]], [w.lower() for w in ["AbC","XYZ"]])\line
print([w.zfill(4) for w in ["7","42"]])\line
\line
\b List – kernmethoden\b0\line
lst=["a"]; lst.append("b"); print(lst)\line
lst=["a"]; lst.extend(["b","c"]); print(lst)\line
lst=["a","c"]; lst.insert(1,"b"); print(lst)\line
lst=["a","b","a"]; lst.remove("a"); print(lst)\line
lst=["a","b","c"]; print(lst.pop(1), lst)\line
lst=["a","b","c"]; del lst[0:2]; print(lst)\line
lst=["a","b","a"]; print(lst.index("a"), lst.count("a"))\line
lst=[3,1,2]; lst.sort(); print(lst); lst=[1,2,3]; lst.reverse(); print(lst)\line
}

