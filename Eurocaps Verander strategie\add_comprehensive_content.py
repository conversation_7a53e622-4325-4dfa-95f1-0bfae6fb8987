import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor

def create_proper_table(doc, title, headers, data):
    """Create a properly formatted table"""
    
    # Add table title
    table_title = doc.add_paragraph()
    table_title_run = table_title.add_run(title)
    table_title_run.font.bold = True
    table_title_run.font.size = Pt(12)
    table_title_run.font.name = 'Arial'
    
    # Create table
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Add headers
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        # Make header bold
        for paragraph in hdr_cells[i].paragraphs:
            for run in paragraph.runs:
                run.font.bold = True
                run.font.size = Pt(11)
                run.font.name = 'Arial'
    
    # Add data rows
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
            # Set font size for data cells
            for paragraph in row_cells[i].paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(10)
                    run.font.name = 'Arial'
    
    doc.add_paragraph()  # Add space after table
    return table

def add_visual_placeholder(doc, caption_text):
    """Add visual placeholder with proper caption"""
    
    # Add placeholder text
    placeholder_para = doc.add_paragraph()
    placeholder_run = placeholder_para.add_run('[VISUAL PLACEHOLDER]')
    placeholder_run.font.size = Pt(12)
    placeholder_run.font.italic = True
    placeholder_run.font.name = 'Arial'
    placeholder_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add caption
    caption_paragraph = doc.add_paragraph()
    caption_run = caption_paragraph.add_run(caption_text)
    caption_run.font.size = Pt(10)
    caption_run.font.italic = True
    caption_run.font.name = 'Arial'
    caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()  # Add space after visual
    return True

def add_chapter_1_comprehensive(doc):
    """Add comprehensive Chapter 1"""
    
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 1: Inleiding', 1)
    
    intro_text = """Euro Caps staat voor een belangrijke uitdaging in de hedendaagse concurrerende markt van koffiecapsules. Als toonaangevende producent van hoogwaardige koffiecapsules voor zowel de retail- als de professionele markt, erkent Euro Caps de noodzaak van continue kwaliteitsverbetering om haar marktpositie te behouden en verder uit te brouwen. In dit kader heeft de organisatie besloten tot de implementatie van Six Sigma, een bewezen methodiek voor procesverbetering en kwaliteitsmanagement.

De implementatie van Six Sigma vereist echter meer dan alleen de introductie van nieuwe werkprocessen en meetmethoden. Het vraagt om een fundamentele organisatieverandering die zowel de structurele als de culturele aspecten van Euro Caps omvat. Deze verandering moet zorgvuldig worden gepland en uitgevoerd om succesvol te zijn en duurzame resultaten te behalen.

Dit adviesrapport richt zich op de vraag hoe Euro Caps op een effectieve en duurzame manier de organisatie kan inrichten en de cultuur kan ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma. Het rapport biedt een uitgebreide analyse van de huidige situatie, schetst de gewenste toekomstsituatie, en presenteert een concrete implementatiestrategie gebaseerd op bewezen verandermanagementtheorieën.

De centrale onderzoeksvraag luidt: "Hoe kan Euro Caps op een effectieve en duurzame manier de organisatie inrichten en de cultuur ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma?"

Om deze hoofdvraag te beantwoorden, worden de volgende deelvragen onderzocht:
1. Wat is de huidige organisatiestructuur en -cultuur van Euro Caps?
2. Welke organisatiestructuur en -cultuur zijn gewenst voor succesvolle Six Sigma implementatie?
3. Welke veranderstrategie is het meest geschikt voor Euro Caps?
4. Hoe kan de implementatie van Six Sigma gefaseerd worden uitgevoerd?
5. Welke communicatiestrategie ondersteunt de verandering het beste?"""
    
    doc.add_paragraph(intro_text)
    
    # 1.1 Deskresearch methode
    doc.add_heading('1.1 Deskresearch methode', 2)
    
    methode_text = """Dit onderzoek is uitgevoerd als deskresearch, waarbij gebruik is gemaakt van bestaande literatuur, theoretische modellen en case studies op het gebied van verandermanagement en organisatieontwikkeling. De methodiek omvat een systematische analyse van relevante bronnen, waaronder:

Primaire bronnen:
- Academische literatuur over verandermanagement (Boonstra, 2018; Kotter, 1996)
- Organisatiecultuur theorieën (Hofstede, Hofstede & Minkov, 2010)
- Veranderstrategieën en -modellen (De Caluwé & Vermaak, 2009)
- Organisatiestructuur theorieën (Mintzberg, 1983)

Secundaire bronnen:
- Case studies van Six Sigma implementaties in vergelijkbare organisaties
- Bedrijfsinformatie en organisatiegegevens van Euro Caps
- Branche-analyses van de koffiecapsule industrie

De analyse is uitgevoerd volgens een gestructureerde aanpak waarbij eerst de theoretische basis wordt gelegd, vervolgens de huidige situatie wordt geanalyseerd, de gewenste situatie wordt gedefinieerd, en ten slotte een implementatiestrategie wordt ontwikkeld. Deze systematische benadering waarborgt een grondige en wetenschappelijk onderbouwde analyse."""
    
    doc.add_paragraph(methode_text)
    
    # 1.2 Leeswijzer
    doc.add_heading('1.2 Leeswijzer', 2)
    
    leeswijzer_text = """Dit rapport is opgebouwd volgens een logische structuur die de lezer stap voor stap door de analyse en aanbevelingen leidt:

Hoofdstuk 2 - Theoretisch kader: Dit hoofdstuk legt de theoretische basis voor het onderzoek door de belangrijkste concepten en modellen te introduceren die worden gebruikt in de analyse. Het behandelt veranderstrategieën volgens Boonstra, De Caluwé's kleurenmodel, Hofstede's cultuurdimensies, Kotter's achtstappenmodel, stakeholderanalyse en de verandercurve van Kübler-Ross.

Hoofdstuk 3 - Huidige situatie: Hier wordt een uitgebreide analyse gepresenteerd van de huidige organisatiestructuur en -cultuur van Euro Caps, gebaseerd op de theoretische modellen uit hoofdstuk 2.

Hoofdstuk 4 - Gewenste situatie: Dit hoofdstuk schetst de ideale organisatiestructuur en -cultuur die nodig zijn voor succesvolle Six Sigma implementatie.

Hoofdstuk 5 - Veranderstrategie en implementatieplan: Het kernhoofdstuk van het rapport, waarin de concrete veranderstrategie wordt gepresenteerd, inclusief een gefaseerd implementatieplan over 21 maanden.

Hoofdstuk 6 - Communicatieplan: Een gedetailleerd communicatieplan dat ondersteunt bij de succesvolle uitvoering van de veranderstrategie.

Hoofdstuk 7 - Conclusie: Een samenvatting van de belangrijkste bevindingen en het antwoord op de centrale onderzoeksvraag.

Het rapport wordt afgesloten met concrete aanbevelingen, een literatuurlijst en een argumentatieschema dat de wetenschappelijke onderbouwing van de stellingen transparant maakt."""
    
    doc.add_paragraph(leeswijzer_text)
    
    return doc

def add_chapter_2_comprehensive(doc):
    """Add comprehensive Chapter 2 with detailed theory"""
    
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 2: Theoretisch kader', 1)
    
    intro_text = """Dit hoofdstuk vormt de theoretische basis voor de analyse en aanbevelingen in dit rapport. Het introduceert en verklaart de belangrijkste concepten en modellen die worden gebruikt om de organisatieverandering bij Euro Caps te analyseren en te plannen. De geselecteerde theorieën zijn bewezen effectief in verandermanagementprocessen en bieden een solide wetenschappelijke basis voor de ontwikkeling van een implementatiestrategie voor Six Sigma."""
    
    doc.add_paragraph(intro_text)
    
    # 2.1 Boonstra
    doc.add_heading('2.1 Veranderstrategieën volgens Boonstra', 2)
    
    boonstra_text = """Jaap Boonstra (2018) onderscheidt in zijn werk over verandermanagement vijf verschillende strategieën voor organisatieverandering, elk met specifieke kenmerken en toepassingsgebieden. Deze strategieën bieden een raamwerk voor het kiezen van de meest geschikte aanpak voor een specifieke organisatie en veranderingssituatie.

De ontwikkelingsstrategie kenmerkt zich door een participatieve benadering waarbij verandering ontstaat door collectief leren en gezamenlijke ontwikkeling. Deze strategie is tijdrovend maar leidt tot duurzame veranderingen omdat medewerkers actief betrokken zijn bij het vormgeven van de nieuwe situatie. De strategie is vooral geschikt voor organisaties met een open cultuur en voldoende tijd voor de verandering.

Een ingrijpende strategie daarentegen is gebaseerd op een top-down benadering waarbij verandering wordt opgelegd door het management. Deze strategie is snel en effectief in crisissituaties, maar kan leiden tot weerstand omdat medewerkers weinig invloed hebben op het veranderingsproces. De strategie is geschikt wanneer snelle actie vereist is en de organisatie hiërarchisch georganiseerd is.

De machtsstrategie legt de nadruk op controle en autoriteit, waarbij verandering wordt afgedwongen door gebruik van formele macht en sancties. Deze strategie kan effectief zijn in sterk gestructureerde omgevingen, maar riskeert compliance zonder commitment van medewerkers.

Een onderhandelingsstrategie zoekt naar compromissen tussen verschillende belangen en stakeholders. Deze strategie vereist tijd en diplomatieke vaardigheden, maar kan leiden tot breed draagvlak omdat alle partijen zich gehoord voelen en hun belangen worden erkend.

Tot slot stimuleert de verleidingsstrategie verandering door inspiratie, voorbeeldgedrag en het creëren van een aantrekkelijke toekomstvisie. Deze strategie werkt goed in culturen die waarde hechten aan autonomie en intrinsieke motivatie, maar vereist charismatisch leiderschap en een overtuigende visie."""
    
    doc.add_paragraph(boonstra_text)
    
    # Add Boonstra visual placeholder
    add_visual_placeholder(doc, 'Figuur 2.1: Overzicht van Boonstra\'s vijf veranderstrategieën met kenmerken en toepassingsgebieden')
    
    return doc

def add_complete_chapter_2(doc):
    """Complete Chapter 2 with all theoretical frameworks"""

    # 2.2 De Caluwé
    doc.add_heading('2.2 Veranderkleuren van De Caluwé', 2)

    caluwe_text = """Het kleurenmodel van De Caluwé en Vermaak (2009) biedt een innovatieve benadering voor het begrijpen en plannen van organisatieverandering door verschillende 'denklogica's' of 'kleuren' te onderscheiden. Elk kleur representeert een fundamenteel verschillende manier van denken over hoe verandering tot stand komt en hoe mensen gemotiveerd kunnen worden om mee te werken aan verandering.

Blauwdrukdenken (rationele benadering) gaat ervan uit dat mensen rationele wezens zijn die overtuigd kunnen worden door logische argumenten, feiten en cijfers. In deze benadering wordt verandering gepland als een rationeel proces waarbij doelen helder worden gedefinieerd, plannen worden gemaakt en stap voor stap worden uitgevoerd. Deze aanpak werkt goed bij technische veranderingen en in organisaties met een sterke planningscultuur.

Geeldrukdenken (politieke benadering) erkent dat organisaties politieke arena's zijn waarin verschillende partijen verschillende belangen hebben. Verandering komt tot stand door onderhandeling, coalitievorming en het vinden van win-win situaties. Deze aanpak is essentieel wanneer er sprake is van conflicterende belangen en machtsstrijd binnen de organisatie.

Rooddrukdenken (relationele benadering) benadrukt het belang van menselijke relaties, emoties en motivatie. Verandering ontstaat door het opbouwen van vertrouwen, het creëren van betrokkenheid en het aanspreken van intrinsieke motivatie. Deze aanpak is cruciaal voor het verkrijgen van commitment en het overwinnen van emotionele weerstand.

Groendrukdenken (lerende benadering) ziet verandering als een leerproces waarbij nieuwe inzichten en vaardigheden worden ontwikkeld door experiment, reflectie en gezamenlijk leren. Deze aanpak is geschikt voor complexe veranderingen waarbij de uitkomst niet vooraf vaststaat en creativiteit vereist is.

Witdrukdenken (emergente benadering) accepteert dat verandering vaak onvoorspelbaar en chaotisch is, en dat organisaties complexe systemen zijn waarin kleine veranderingen grote effecten kunnen hebben. Deze aanpak richt zich op het creëren van condities waarin gewenste verandering kan ontstaan, zonder deze volledig te willen controleren."""

    doc.add_paragraph(caluwe_text)

    add_visual_placeholder(doc, 'Figuur 2.2: De Caluwé\'s kleurenmodel met vijf denklogica\'s voor organisatieverandering')

    # 2.3 Gap-analyse & Hofstede
    doc.add_heading('2.3 Gap-analyse & Hofstede-model', 2)

    gap_hofstede_text = """Een Gap-analyse is een fundamentele tool in verandermanagement die het verschil tussen de huidige situatie (AS-IS) en de gewenste toekomstsituatie (TO-BE) systematisch in kaart brengt. Deze analyse helpt organisaties om te begrijpen waar zij staan, waar zij naartoe willen, en welke stappen nodig zijn om de gewenste verandering te realiseren.

Het proces van Gap-analyse omvat verschillende fasen:
1. Huidige situatie analyse: Een grondige inventarisatie van de bestaande processen, structuren, cultuur en prestaties
2. Gewenste situatie definitie: Het helder formuleren van de doelstellingen en de gewenste toekomstsituatie
3. Gap identificatie: Het systematisch identificeren van de verschillen tussen de huidige en gewenste situatie
4. Actieplan ontwikkeling: Het formuleren van concrete acties om de geïdentificeerde gaps te overbruggen

Voor de analyse van organisatiecultuur wordt gebruik gemaakt van Hofstede's cultuurdimensies (Hofstede, Hofstede & Minkov, 2010). Dit model onderscheidt zes fundamentele dimensies waarop organisatieculturen kunnen verschillen:

Machtsafstand: De mate waarin minder machtige leden van een organisatie accepteren dat macht ongelijk verdeeld is. Hoge machtsafstand betekent hiërarchische structuren en formele autoriteit, lage machtsafstand duidt op meer egalitaire verhoudingen.

Individualisme versus Collectivisme: De mate waarin individuen geïntegreerd zijn in groepen. Individualistische culturen benadrukken persoonlijke prestaties en autonomie, collectivistische culturen leggen de nadruk op groepsharmonie en loyaliteit.

Masculien versus Feminien: Masculiene culturen waarderen competitie, prestatie en succes, terwijl feminiene culturen meer waarde hechten aan samenwerking, kwaliteit van leven en zorg voor anderen.

Onzekerheidsvermijding: De mate waarin een cultuur zich bedreigd voelt door onzekere of onbekende situaties. Hoge onzekerheidsvermijding leidt tot meer regels en procedures, lage onzekerheidsvermijding tot meer flexibiliteit en risico-acceptatie.

Lange- versus kortetermijngerichtheid: De mate waarin een cultuur gericht is op toekomstige beloningen versus onmiddellijke resultaten. Langetermijngerichte culturen investeren in de toekomst, kortetermijngerichte culturen focussen op snelle resultaten.

Toegeeflijkheid versus Terughoudendheid: De mate waarin een cultuur vrije expressie van natuurlijke menselijke driften toestaat. Toegeeflijke culturen staan plezier en genot toe, terughoudende culturen reguleren dit door strikte sociale normen."""

    doc.add_paragraph(gap_hofstede_text)

    add_visual_placeholder(doc, 'Figuur 2.3: Gap-analyse model voor het identificeren van veranderacties')
    add_visual_placeholder(doc, 'Figuur 2.4: Hofstede\'s zes cultuurdimensies voor organisatieanalyse')

    return doc

def add_remaining_theory(doc):
    """Add remaining theoretical frameworks"""

    # 2.4 Kotter
    doc.add_heading('2.4 Kotter\'s 8 Stappenmodel', 2)

    kotter_text = """John Kotter (1996) heeft een van de meest invloedrijke en bewezen modellen voor organisatieverandering ontwikkeld. Zijn achtstappenmodel biedt een gestructureerde aanpak voor het leiden van succesvolle verandering en is gebaseerd op uitgebreid onderzoek naar zowel succesvolle als gefaalde veranderingsinitiatieven.

Stap 1 - Creëer een gevoel van urgentie: Verandering begint met het creëren van een gedeeld besef dat verandering noodzakelijk is. Dit vereist het identificeren en communiceren van bedreigingen en kansen, en het overtuigen van mensen dat de status quo niet langer acceptabel is.

Stap 2 - Vorm een leidende coalitie: Succesvolle verandering vereist een groep invloedrijke mensen die samenwerken als team. Deze coalitie moet voldoende macht hebben om de verandering te leiden en moet bestaan uit mensen met verschillende vaardigheden en perspectieven.

Stap 3 - Ontwikkel een visie en strategie: Een heldere visie helpt mensen te begrijpen waarom verandering nodig is en hoe de toekomst eruit zal zien. De strategie beschrijft hoe deze visie gerealiseerd kan worden.

Stap 4 - Communiceer de veranderingsvisie: De visie moet breed en herhaaldelijk gecommuniceerd worden door alle beschikbare kanalen. Leiders moeten de visie niet alleen uitdragen in woorden, maar ook in hun gedrag.

Stap 5 - Creëer draagvlak voor actie: Obstakels die de implementatie van de visie belemmeren moeten worden weggenomen. Dit kunnen structurele barrières zijn, maar ook mensen die weerstand bieden aan de verandering.

Stap 6 - Genereer korte termijn successen: Zichtbare verbeteringen op korte termijn helpen om momentum te behouden en sceptici te overtuigen. Deze successen moeten gepland en gevierd worden.

Stap 7 - Consolideer verbeteringen en produceer nog meer verandering: Vroege successen mogen niet leiden tot zelfgenoegzaamheid. In plaats daarvan moeten ze gebruikt worden om nog meer veranderingen door te voeren.

Stap 8 - Veranker nieuwe benaderingen in de cultuur: Nieuwe gedragingen moeten verankerd worden in de organisatiecultuur om ervoor te zorgen dat ze blijvend zijn. Dit vereist het aantonen van verbanden tussen nieuwe gedragingen en organisatiesucces."""

    doc.add_paragraph(kotter_text)

    add_visual_placeholder(doc, 'Figuur 2.5: Kotter\'s 8-stappenmodel voor succesvolle organisatieverandering')

    return doc

if __name__ == "__main__":
    print("=== Adding Comprehensive Content ===")

    # Load existing document
    doc = Document('Adviesrapport_Veranderingsmanagement_GECORRIGEERD_STRUCTUUR.docx')

    # Add comprehensive chapters
    doc = add_chapter_1_comprehensive(doc)
    doc = add_chapter_2_comprehensive(doc)
    doc = add_complete_chapter_2(doc)
    doc = add_remaining_theory(doc)

    # Save updated document
    doc.save('Adviesrapport_Veranderingsmanagement_UITGEBREID_DEEL2.docx')
    print("Comprehensive content part 2 added successfully!")
    print("\n=== UITBREIDINGEN DEEL 2 ===")
    print("✅ Hoofdstuk 2 volledig uitgewerkt met alle theorieën")
    print("✅ De Caluwé kleurenmodel uitgebreid")
    print("✅ Gap-analyse en Hofstede model gedetailleerd")
    print("✅ Kotter's 8-stappenmodel volledig uitgewerkt")
    print("✅ Meer diepgang en wetenschappelijke onderbouwing")
